# Specifikace pro Interaktivní UI Komponenty v Chatu <PERSON>dy

**Verze:** 1.0
**Cíl:** <PERSON><PERSON><PERSON><PERSON> systém, kter<PERSON> umožní AI agentovi dynamicky generovat a zpracovávat interaktivní prvky (tlačítka, výběry) v rámci konverzačního rozhraní.

---

## 1. Filozofie a Princip

Systém je založen na principu **"sémantických značek" (semantic tags)**. Backendový agent ve své textové odpovědi vygeneruje speciální, strojov<PERSON> čitelnou značku. Frontendový klient tuto značku rozpozná, skryje ji před uživatelem a místo ní zobrazí odpovídající interaktivní UI prvek. Odpověď uživatele je pak poslána zpět na backend ve strukturovaném formátu.

Tento přístup dává plnou autonomii agentovi, aby se s<PERSON><PERSON> r<PERSON>, kdy je vhodné použít interaktivní prvek pro zjednodušení a zrychlení komunikace.

---

## 2. Definice Sémantických Značek

Definujeme sadu značek ve formátu podobném XML/HTML. Každá značka má jasně daný název a atributy.

### 2.1. Tlačítka s Rychlými Odpověďmi (`<QUICK_REPLIES>`)

*   **Účel:** Nabídnout uživateli sadu předdefinovaných, jedno-klikových odpovědí. Ideální pro potvrzení, jednoduché volby nebo navigaci v dialogu.
*   **Syntaxe:**
    ```
    <QUICK_REPLIES options="Odpověď A|Odpověď B|Další možnost">
    Text otázky, která se zobrazí nad tlačítky.
    </QUICK_REPLIES>
    ```
*   **Příklad Použití v Odpovědi Agenta:**
    > Rozumím. Abychom se posunuli dál, souhlasíte s navrhovaným postupem?
    > <QUICK_REPLIES options="Ano, souhlasím|Ne, mám dotaz|Chci jinou možnost">
    > </QUICK_REPLIES>
*   **Zobrazení na Frontendu:** Zobrazí se text "Rozumím..." a pod ním tři tlačítka: "Ano, souhlasím", "Ne, mám dotaz", "Chci jinou možnost".
*   **Odpověď z Frontendu:** Když uživatel klikne na tlačítko, frontend pošle na backend text z tlačítka, např. `{"message": "Ano, souhlasím"}`.

### 2.2. Výběr Jedné Možnosti (`<SINGLE_CHOICE>`)

*   **Účel:** Prezentovat uživateli seznam možností, ze kterého si může vybrat právě jednu. Používá se pro otázky s jednou správnou odpovědí.
*   **Syntaxe:**
    ```
    <SINGLE_CHOICE key="nazev_promenne" options="Možnost 1|Možnost 2|Možnost 3">
    Text otázky pro uživatele.
    </SINGLE_CHOICE>
    ```
    *   `key`: Název proměnné, pod kterou se uloží výsledek.
*   **Příklad Použití v Odpovědi Agenta:**
    > Který typ výzkumu preferujete?
    > <SINGLE_CHOICE key="research_type" options="Kvantitativní|Kvalitativní|Smíšený">
    > </SINGLE_CHOICE>
*   **Zobrazení na Frontendu:** Zobrazí se otázka a pod ní sada "radio buttons" (přepínačů).
*   **Odpověď z Frontendu:** Frontend pošle strukturovanou odpověď: `{"user_input": {"research_type": "Kvantitativní"}}`.

### 2.3. Výběr Více Možností (`<MULTI_CHOICE>`)

*   **Účel:** Prezentovat uživateli seznam možností, ze kterého si může vybrat jednu nebo více.
*   **Syntaxe:**
    ```
    <MULTI_CHOICE key="nazev_promenne" options="Možnost A|Možnost B|Možnost C">
    Text otázky pro uživatele.
    </MULTI_CHOICE>
    ```
*   **Příklad Použití v Odpovědi Agenta:**
    > Jaké typy analýz by pro vás byly nejpřínosnější?
    > <MULTI_CHOICE key="pozadovane_analyzy" options="Základní přehled|Matice problémů|Mosaic">
    > </MULTI_CHOICE>
*   **Zobrazení na Frontendu:** Zobrazí se otázka a pod ní sada "checkboxů" (zaškrtávacích políček).
*   **Odpověď z Frontendu:** Frontend pošle pole hodnot: `{"user_input": {"pozadovane_analyzy": ["basic_overview", "ipa_matrix"]}}`.

---

## 3. Implementační Kroky

### 3.1. Úkoly pro Backendového Kódéra

1.  **Vylepšit Systémový Prompt Agenta:** Do hlavního systémového promptu přidat instrukce o existenci a použití těchto sémantických značek. Agent musí být instruován, aby je použil, když to uzná za vhodné pro zjednodušení konverzace.
    *   *Příklad instrukce:* "Pokud potřebuješ od uživatele získat výběr z několika jasně daných možností, použij značky `<SINGLE_CHOICE>` nebo `<MULTI_CHOICE>`, abys mu usnadnil odpověď."
2.  **Upravit Zpracování Vstupu:** Logika v `api_server.py` musí být schopna zpracovat jak jednoduchý textový vstup (`"message": "..."`), tak strukturovaný vstup (`"user_input": {...}`).

### 3.2. Úkoly pro Frontendového Kódéra

1.  **Implementovat Parser Značek v `chat.js`:**
    *   Vytvořit funkci, která po přijetí odpovědi od agenta prohledá text na přítomnost značek (`<QUICK_REPLIES ...>`, `<SINGLE_CHOICE ...>`, atd.) pomocí regulárních výrazů.
2.  **Dynamické Vykreslování UI:**
    *   Pokud je nalezena značka:
        *   Text uvnitř značky se zobrazí jako otázka.
        *   Na základě typu značky a jejích atributů (`options`) se dynamicky vytvoří a zobrazí příslušné HTML elementy (tlačítka, radio buttons, checkboxy).
        *   Hlavní textové pole pro vstup se dočasně deaktivuje.
    *   Pokud značka nalezena není, zobrazí se odpověď jako běžná textová zpráva.
3.  **Odesílání Strukturovaných Odpovědí:**
    *   Implementovat logiku, která po interakci uživatele s UI prvkem (kliknutí na tlačítko, potvrzení výběru) sestaví správný JSON objekt (`{"message": ...}` nebo `{"user_input": ...}`) a pošle ho na backend.
    *   Po odeslání se interaktivní prvky odstraní a chat se vrátí do normálního textového režimu.

Tato specifikace poskytuje kompletní a jasný plán pro implementaci bohatšího a uživatelsky přívětivějšího rozhraní, přičemž zachovává plnou autonomii a inteligenci na straně AI agenta.