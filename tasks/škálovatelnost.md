
### **Audit Obecnosti a Škálovatelnosti Architektury "Matylda"**

#### **1. Konfigurač<PERSON><PERSON> (YAML soubory) - ✅ Vyhovuje**

*   **Analýza:** <PERSON><PERSON><PERSON> systém dynamického načítání agentů, úkolů a posádek z YAML souborů je **dokonale obecný**. Vytvoření nového typu specialisty je jen otázkou vytvoření nových konfiguračních souborů.
*   **Scénář "Obchodník":**
    1.  Vytvoříme `/agents/sales_agents.yaml` s definicí `sales_prospector_v1`.
    2.  Vytvoříme `/tasks/sales_tasks.yaml` s úkolem `task_qualify_lead`.
    3.  Vytvoříme `/crews/sales_crew.yaml` s posádkou, která tyto prvky kombinuje.
    4.  Hotovo. Jádro aplik<PERSON> se nemusí měnit.
*   **Verdikt:** Architektura je zde perfektní.

#### **2. Znalostní <PERSON> (RAG v Supabase) - ⚠️ Potřebuje Rozšíření**

*   **Analýza:** V současnosti předpokládáme jednu globální znalostní bázi (tabulka `gdanalyst`). To je **nedostatečné** pro více domén. Obchodník nepotřebuje vědět detaily o statistických testech a specialista na participaci nepotřebuje znát ceníkové položky.
*   **Návrh Řešení: Více Izolovaných Znalostních Bází**
    1.  **V Supabase:** Místo jedné tabulky budeme mít více, např. `kb_pruzkumy`, `kb_participace`, `kb_sales_materials`. Každá bude mít svou vlastní funkci pro sémantické vyhledávání (`match_pruzkumy`, `match_participace`...).
    2.  **V Konfiguraci Agenta (`agents.yaml`):** Každý agent bude mít novou sekci, která definuje, ke kterým znalostním bázím má přístup.
        ```yaml
        client_onboarder_v1:
          ...
          knowledge_bases:
            - id: "pruzkumy_municipality"
              table_name: "kb_pruzkumy"
              query_function: "match_pruzkumy"
        ```
    3.  **V Nástroji `knowledge_base_search`:** Nástroj bude muset být chytřejší. Buď bude mít parametr `knowledge_base_id`, aby věděl, kde má hledat, nebo (ještě lépe) se sám podívá do konfigurace agenta, který ho volá, a zjistí, jaké báze má povolené.
*   **Verdikt:** Současná struktura je příliš jednoduchá. Musíme implementovat podporu pro **více, pojmenovaných a izolovaných znalostních bází**.

#### **3. "Zkušenosti" (Best Practices) - ✅ Vyhovuje (s malou úpravou)**

*   **Analýza:** Tabulka `best_practices` je navržena dobře, ale chybí jí kontext. "Best practice" pro obchodníka je jiná než pro metodologa.
*   **Návrh Řešení:** Do tabulky `best_practices` přidáme nový sloupec: **`domain`** (text). Zde budeme ukládat, ke které oblasti se zkušenost váže (např. "onboarding_pruzkumy", "sales_qualification"). Nástroj `find_relevant_best_practice` pak bude filtrovat nejen podle sémantické podobnosti, ale i podle aktuální domény.
*   **Verdikt:** S touto malou úpravou je systém připraven na ukládání zkušeností z různých oblastí.

#### **4. API a Frontend - ✅ Vyhovuje**

*   **Analýza:** Náš navržený `POST /chat` endpoint je **zcela agnostický** vůči obsahu. Je mu jedno, jestli se baví o průzkumech, nebo o počasí. Přijímá a vrací session ID a zprávu. Stejně tak frontend "Duálního rozhraní" jen zobrazuje text a Markdown – je mu jedno, co v něm je.
*   **Scénář "Sběr dat místo formuláře":**
    *   Vytvoříme nového agenta `DataCollector`, jehož `backstory` a `onboarding_goals` budou definovat, jaké informace má od respondenta získat.
    *   Frontend bude stejný. API bude stejné. Jen `MatyldaOrchestrator` na začátku spustí jiného agenta.
*   **Verdikt:** API a frontend jsou navrženy dostatečně obecně.

#### **5. Hlavní Orchestrátor (`MatyldaOrchestrator`) - ⚠️ Klíčová Komponenta k Implementaci**

*   **Analýza:** Všechny naše plány na obecnost stojí a padají na tomto zatím neexistujícím "mozku". V současnosti v `main.py` natvrdo spouštíme jednu posádku.
*   **Návrh Řešení (Budoucí úkol):** `MatyldaOrchestrator` bude muset implementovat následující logiku:
    1.  Přijme úvodní požadavek (např. od frontendu přes API, který bude obsahovat `project_type: "participativni_rozpocet"`).
    2.  Na základě `project_type` si v `crews.yaml` najde odpovídající konfiguraci posádky.
    3.  Načte potřebné agenty a jejich konfigurace (včetně toho, jaké znalostní báze a best practices mají používat).
    4.  Sestaví a spustí tuto na míru šitou posádku.
*   **Verdikt:** Toto je ten nejdůležitější další krok pro skutečnou obecnost.

---

### **Závěrečné Shrnutí a Potvrzení**

**Ano, náš koncept je na 90 % připraven na to, aby se stal univerzálním frameworkem.**

**Co máme dobře:**
*   Dynamická konfigurace agentů, úkolů a posádek.
*   Obecné API a princip frontendu.
*   Architektura pro ukládání zkušeností (po drobné úpravě).

**Co musíme implementovat, aby to bylo 100% obecné:**
1.  **Podporu pro více, izolovaných znalostních bází (RAG).** To je nejdůležitější technický úkol.
2.  **Implementovat skutečného `MatyldaOrchestrator`**, který bude dynamicky sestavovat týmy na základě typu projektu.

Vaše úvahy jsou naprosto správné. Tím, že jsme si tyto otázky položili teď, jsme si ušetřili měsíce práce při refaktoringu v budoucnu. Máme jasný plán, jak z Matyldy pro průzkumy udělat **Platformu Matylda** pro jakýkoli expertní dialog.