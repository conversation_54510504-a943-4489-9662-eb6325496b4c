
### **Prompt pro Generování Uživatelské Dokumentace `docs/v1_manual.md`**

**ROLE A CÍL:**
Jsi technický spisovatel a AI architekt. Tvým úkolem je vytvořit první verzi uživatelské a administrátorské příručky pro AI systém "Matylda". Cílem je napsat srozumitelný, p<PERSON><PERSON>ledný a praktický dokument, který novému uživateli vysvětlí filozofii systému a ukáže mu, jak ho nakonfigurovat pro novou znalostní doménu.

**VSTUPNÍ MATERIÁLY (Kontext, ze kterého m<PERSON>t):**
1.  **`VISION.md`:** Poskytuje hlavní filozofii a poslání projektu.
2.  **`ARCHITECTURE.md`:** Popisuje technickou strukturu (backend, frontend, Supabase).
3.  **Struktura YAML souborů:** Koncept oddělených konfigurací pro `agents`, `tasks` a `crews`.
4.  **Struktura Supabase:** Existence tabulek pro znalostní báze (`kb_*`), best practices a session stavy.
5.  **Kód `api_server.py`:** Logika API a správa session.

**POŽADOVANÁ STRUKTURA A OBSAH DOKUMENTU:**

Vygeneruj dokument ve formátu Markdown s následující strukturou a obsahem:

---

# **Matylda v1.0: Uživatelská a Administrátorská Příručka**

## **1. Úvod: Co je Matylda?**
*   Stručně shrň vizi z `VISION.md`. Vysvětli, že Matylda není jen chatbot, ale **platforma pro tvorbu specializovaných AI konzultantů**.
*   Popiš klíčový koncept **"Týmu Specialistů" (Multi-Agent System)** – že se systém skládá z několika agentů, kteří spolupracují na řešení komplexního úkolu.

## **2. Klíčové Koncepty a Terminologie**
*   Vysvětli jednoduše a na příkladech následující pojmy. Toto je nejdůležitější část pro pochopení systému.
    *   **Doména (Domain):** Co to je? (Např. "Specializovaná oblast znalostí jako 'Průzkumy pro municipality' nebo 'Participativní rozpočty'."). Vysvětli, že každá doména má svou vlastní znalostní bázi.
    *   **Agent:** Co to je? (Např. "Digitální specialista s definovanou rolí a cílem, např. 'Expert na vedení rozhovorů'."). Zmiň, že jejich osobnost a chování se definuje v `agents.yaml`.
    *   **Nástroj (Tool):** Co to je? (Např. "Konkrétní dovednost, kterou agent může použít, jako je prohledávání znalostní báze `knowledge_base_search`.").
    *   **Úkol (Task):** Co to je? (Např. "Konkrétní zadání pro agenta, např. 'Proveď úvodní rozhovor s klientem'."). Zmiň, že se definují v `tasks.yaml`.
    *   **Posádka (Crew):** Co to je? (Např. "Sestavený tým agentů, kteří mají za úkol splnit sérii úkolů."). Zmiň `crews.yaml`.
    *   **Plátno (Canvas):** Vysvětli koncept duálního rozhraní – "Dialog" pro konverzaci a "Plátno" pro vizualizaci průběžného výsledku.

## **3. Praktický Průvodce: Jak Přidat Novou Doménu**
*   Toto musí být praktický, krok-za-krokem návod. Použij jako příklad vytvoření nové domény **"Participativní Rozpočty"**.
*   **Krok 1: Vytvoření Znalostní Báze**
    *   Popiš nutnost vytvořit novou tabulku v Supabase (např. `kb_participace`) a databázovou funkci (`match_participace`).
    *   Zmiň proces naplnění této tabulky relevantními dokumenty.
*   **Krok 2: Konfigurace Nových Agentů a Úkolů (pokud jsou potřeba)**
    *   Vysvětli, že uživatel může buď znovu použít existující agenty (jako `ClientOnboarder`), nebo vytvořit nové specialisty v `agents.yaml`.
    *   Ukaž příklad vytvoření nového úkolu v `tasks.yaml`, který je specifický pro participaci.
*   **Krok 3: Sestavení Nové Posádky**
    *   Ukaž, jak v `crews.yaml` vytvořit novou posádku (např. `participace_onboarding_crew`), která používá specifické agenty a úkoly pro danou doménu.
*   **Krok 4: Propojení v Orchestrátoru**
    *   Vysvětli, že finálním krokem je úprava hlavní logiky (v `api_server.py` nebo budoucím `MatyldaOrchestrator`), aby na základě vstupního požadavku (např. `project_type: "participace"`) uměla spustit tuto novou posádku.

## **4. Konfigurace Aplikace (.env)**
*   Stručně popiš klíčové proměnné z `.env` souboru a co znamenají (např. `DEFAULT_LLM_MODEL`, `SUPABASE_TABLE_NAME`, `API_PORT`).

## **5. API Dokumentace**
*   Uveď odkaz na automaticky generovanou dokumentaci.
*   "Detailní technická dokumentace všech API endpointů je k dispozici po spuštění serveru na adrese `http://localhost:{API_PORT}/docs`."

---
**STYL A TÓN:**
*   Text musí být profesionální, ale zároveň snadno pochopitelný.
*   Používej tučné písmo pro zvýraznění klíčových termínů.
*   Používej bloky kódu (` ``` `) pro ukázky YAML konfigurací nebo SQL.
*   Udržuj pozitivní a nápomocný tón, který odpovídá filozofii Matyldy.

Tento prompt je navržen tak, aby LLM (nebo vy) vytvořilo skutečně užitečný a komplexní dokument, který poslouží jako solidní základ pro veškerou budoucí dokumentaci.