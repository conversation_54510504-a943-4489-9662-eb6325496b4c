# LOGOVÁNÍ V PROJEKTU MATYLDA---

### **Analýza Potřeb: Co Musíme Logovat a Proč?**

Musíme si položit otázku: **Jaké informace potřebují naši budoucí agenti, aby mohli dělat svou práci geniálně?**

1.  **`QualitySupervisor` (Kvalitář/Mentor):**
    *   **Potřeba:** <PERSON>sí pochopit, *proč* agent udělal určité rozhodnutí. Nestačí mu vidět jen "otázka -> odpověď". Potřebuje vidět "myšlenkový pochod".
    *   **Data, která potřebuje:**
        *   <PERSON><PERSON><PERSON> dialog (to máme).
        *   **Který nástroj byl v daném kroku použit?** (`tool_name`)
        *   **S jakým vstupem byl nástroj volán?** (`tool_input`)
        *   **Co nástroj vrátil?** (`tool_output` / `observation`)
        *   **Finální hodnocení konverzace:** Byla <PERSON>? Proč? (`success_flag`, `failure_reason`).
        *   **KPI:** Jak dlouho konverzace trvala? Kolikrát musel zasáhnout RAG? Jaké byly náklady?

2.  **`MatyldaOrchestrator` (Budoucí Manažer):**
    *   **Potřeba:** Musí vědět, v jakém stavu je každá "zakázka" a kteří agenti na ní pracovali.
    *   **Data, která potřebuje:**
        *   Která `Crew` (posádka) byla spuštěna.
        *   Kteří `Agenti` se zúčastnili.
        *   Jaké `Úkoly` plnili.
        *   Finální výstup celého procesu (např. JSON brief).

3.  **My (Lidští Vývojáři/Supervizoři):**
    *   **Potřeba:** Potřebujeme snadno ladit, identifikovat chyby a sledovat výkon systému v čase.
    *   **Data, která potřebujeme:** Vše výše uvedené, plus technické detaily jako `timestamp`, `session_id`, `request_id`, verze agenta atd.

---

### **Návrh Architektury Logování v Supabase**

Navrhuji vytvořit **dvě nové, specializované tabulky** v Supabase. Jedna bude pro vysokoúrovňový přehled konverzací a druhá pro detailní záznam každého kroku.

#### **Tabulka 1: `conversation_sessions`**

Tato tabulka nahradí a rozšíří naši stávající `chat_sessions`. Bude sloužit jako hlavní přehled.

*   **`session_id`** (`uuid`, Primary Key): Unikátní ID celé konverzace.
*   `created_at` (`timestamptz`): Kdy konverzace začala.
*   `updated_at` (`timestamptz`): Kdy proběhla poslední interakce.
*   `initial_request` (`text`): Původní požadavek klienta.
*   `status` (`text`): Aktuální stav (`active`, `completed`, `failed`).
*   `final_brief_json` (`jsonb`, nullable): Finální JSON brief po úspěšném dokončení.
*   `assigned_crew_id` (`text`): ID posádky, která na úkolu pracovala.
*   `kpi_conversation_length` (`int4`): Počet kol dialogu.
*   `kpi_total_cost` (`float4`, nullable): Celkové odhadované náklady na LLM volání.

#### **Tabulka 2: `conversation_steps`**

Toto je srdce našeho logování. Každý "myšlenkový cyklus" agenta bude jeden řádek v této tabulce.

*   **`step_id`** (`uuid`, Primary Key): Unikátní ID tohoto kroku.
*   **`session_id`** (`uuid`, Foreign Key -> `conversation_sessions.session_id`): Ke které konverzaci patří.
*   `created_at` (`timestamptz`): Čas vytvoření kroku.
*   `step_order` (`int4`): Pořadí kroku v konverzaci (1, 2, 3...).
*   `agent_id` (`text`): Který agent tento krok provedl (např. `client_onboarder_v1`).
*   `human_input` (`text`, nullable): Zpráva od člověka v tomto kroku.
*   **`agent_thought`** (`text`, nullable): **Klíčový sloupec!** Zde uložíme "myšlenkový pochod" agenta, který jsme se rozhodli logovat.
*   `tool_used` (`text`, nullable): Název použitého nástroje (např. `knowledge_base_search`).
*   `tool_input` (`jsonb`, nullable): Vstup, se kterým byl nástroj volán.
*   `tool_observation` (`text`, nullable): Výstup, který nástroj vrátil.
*   `agent_response` (`text`): Finální odpověď agenta klientovi v tomto kroku.
*   `embedding`: `vector` (Vektorová reprezentace `agent_thought` a `human_input` pro budoucí sémantickou analýzu selhání/úspěchů).

---

### **Implementace a Integrace (Zadání pro Kódéra)**

#### **V `api_server.py` (nebo v logice agenta):**

1.  **Refaktoring Správy Session:**
    *   Místo dočasného slovníku `active_sessions` bude API nyní komunikovat s tabulkou `conversation_sessions`.
    *   Při prvním požadavku se vytvoří nový záznam v `conversation_sessions`.
    *   Při každém dalším se záznam aktualizuje (hlavně `updated_at`).

2.  **Implementace Detailního Logování Kroků:**
    *   Po každém volání agenta (po každé zprávě od klienta) musíme **zachytit veškeré informace** o jeho "myšlenkovém cyklu". Frameworky jako CrewAI a LangChain poskytují "callbacks" nebo "listeners", které toto umožňují.
    *   Musíme vytvořit funkci, která se "zahákne" do exekuce agenta a po jejím dokončení posbírá všechny potřebné informace (`agent_thought`, `tool_used` atd.).
    *   Tyto informace pak uloží jako **nový řádek** do tabulky `conversation_steps`.

#### **Jak to Budou Agenti Používat (YAML Konfigurace)?**

Vaše myšlenka na předávání informací přes YAML je správná. Můžeme vylepšit definici nástrojů.

**Nástroj `find_relevant_best_practice` (vstupní argumenty):**
*   `current_task_description: str`
*   `full_conversation_history: list[dict]`
*   **`failed_steps_analysis: str` (NOVÉ):** Sem by `QualitySupervisor` mohl předat výstup své analýzy o tom, které kroky selhaly, a nástroj by se pokusil najít zkušenost, která řeší podobné selhání.

**Úkol `learn_from_project_outcome` (vstupní kontext):**
*   Tento úkol už nebude dostávat jen textový přepis. Jeho `description` bude obsahovat proměnnou, do které `MatyldaOrchestrator` vloží **celou sadu záznamů z `conversation_steps`** pro danou session.
*   **Příklad:**
    ```yaml
    description: >
      Proveď "post-mortem" analýzu projektu na základě kompletního záznamu rozhodovacích kroků: '{steps_log}'. 
      Identifikuj...
    ```

**Závěr:**
Tato architektura logování nám dává neuvěřitelnou moc. Neukládáme jen chat, ukládáme **auditovatelný záznam rozhodování naší AI**. `QualitySupervisor` pak může nad těmito daty provádět komplexní analýzy, hledat vzorce selhání a úspěchů a skutečně se učit. Je to základ pro vytvoření robustního, transparentního a neustále se zlepšujícího systému.