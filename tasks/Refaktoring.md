### **Zadání pro AI Kódéra: Refaktoring na Architekturu "Týmu Specialistů"**

**PROJEKT:** Matylda
**ÚKOL:** Přepracovat stávající logiku konverzačního cyklu na novou, pokročilou architekturu založenou na třech specializovaných agentech (`Onboarder`, `CanvasArchitect`, `InterfaceAgent`), kteří spolupracují v reálném čase.

**Cíl:** Dosáhnout kvalitativního skoku v inteligenci a uživatelském zážitku. Agentura musí přestat být "robotickým zapisovačem" a stát se skutečným "strategickým partnerem", který poskytuje jak empatický dialog, tak průběžně aktualizovaný vizuální výstup.

---

### **DETAILNÍ TECHNICKÁ SPECIFIKACE**

#### **ČÁST 1: DEFINICE NOVÝCH ROLÍ A ÚKOLŮ (v YAML souborech)**

**Akce:** Upravit a vytvořit nové konfigurační soubory.

1.  **V souboru `/agents/agents.yaml`:**
    *   **Přejmenovat a upravit `client_onboarder_v1` na `strategic_onboarder_v1`:** Jeho nová role je čistě strategická. Odstraňte z jeho `backstory` veškeré instrukce o tom, jak má mluvit s klientem (to bude dělat jiný agent).
        ```yaml
        strategic_onboarder_v1:
          role: "Logický a Strategický Motor Onboardingu"
          goal: >
            Na základě historie konverzace a aktuálního stavu zadání analyzovat situaci
            a rozhodnout o nejlepším dalším logickém kroku.
          backstory: >
            Jsi čistý stratég. Tvým úkolem je analyzovat stav a rozhodnout, zda je potřeba 
            získat novou informaci, upřesnit stávající, nebo navrhnout řešení, pokud je klient v koncích.
            Tvůj výstup není určen pro klienta, ale pro další agenty v systému.
        ```
    *   **Vytvořit nového agenta `interface_agent_v1`:**
        ```yaml
        interface_agent_v1:
          role: "Empatický Komunikační Specialista"
          goal: "Transformovat strohé logické pokyny na přirozený, přátelský a efektivní dialog pro klienta."
          backstory: >
            Jsi mistr komunikace. Bereš logické pokyny od stratéga a 'polidšťuješ' je.
            Vždy potvrzuješ, co klient řekl, používáš zdvořilostní fráze a zajišťuješ,
            aby konverzace byla plynulá a příjemná. Jsi hlasem Matyldy.
        ```
    *   **Vytvořit nového agenta `canvas_architect_v1`:**
        ```yaml
        canvas_architect_v1:
          role: "Vizuální Syntetizátor Projektového Zadání"
          goal: "V reálném čase vytvářet a aktualizovat přehledný, strukturovaný dokument (plátno), který vizualizuje aktuální stav klientského zadání."
          backstory: >
            Jsi informační architekt. Tvým úkolem je transformovat surová data a stavové 
            informace do vizuálně čistého a srozumitelného Markdown dokumentu. Udržuješ
            'jediný zdroj pravdy' o stavu projektu viditelný pro klienta.
        ```

2.  **V souboru `/tasks/tasks.yaml`:**
    *   Smazat všechny staré onboardingové úkoly.
    *   Vytvořit tři nové, korespondující úkoly:
        ```yaml
        task_decide_next_step:
          description: "Analyzuj historii chatu: {chat_history} a aktuální stav briefu: {brief_state}. Rozhodni o dalším logickém kroku a vrať strukturovaný JSON s tvým myšlenkovým pochodem, další akcí a aktualizovaným stavem briefu."
          expected_output: "JSON objekt obsahující klíče: 'thought_process', 'next_action', 'suggested_instruction', 'updated_brief_state'."
          agent: strategic_onboarder_v1

        task_format_canvas_output:
          description: "Vezmi aktuální JSON stavu briefu: {brief_state}. Převeď ho do přehledného, profesionálně vypadajícího Markdown dokumentu."
          expected_output: "Textový řetězec ve formátu Markdown, který vizualizuje stav projektu."
          agent: canvas_architect_v1
        
        task_format_chat_response:
          description: "Vezmi logický pokyn: {instruction_from_strategist} a celou historii chatu: {chat_history}. Zformuluj z toho přirozenou, empatickou a jasnou textovou odpověď pro klienta."
          expected_output: "Finální textová zpráva, která se zobrazí klientovi v chatu."
          agent: interface_agent_v1
        ```

#### **ČÁST 2: REFAKTORING HLAVNÍ LOGIKY (PYTHON)**

**Akce:** Přepracovat hlavní konverzační smyčku v `api_server.py` nebo `interactive_main.py`.

1.  **Odstranit starou `Crew`:** Zrušit stávající posádku, která dělala vše najednou.

2.  **Implementovat Nový Cyklus "Tří Agentů":** Uvnitř `POST /chat` endpointu implementuj následující logiku:
    1.  Načti stav konverzace (`chat_history`, `brief_state`) pro dané `session_id`.
    2.  **Spusť Posádku 1 (Stratég):** Vytvoř a spusť jednoduchou posádku s agentem `strategic_onboarder_v1` a úkolem `task_decide_next_step`. Jako vstup jí předej `chat_history` a `brief_state`.
    3.  **Zpracuj výstup Stratéga:** Získej jeho výstupní JSON (`{thought_process, next_action, ...}`). Ulož si jeho `updated_brief_state` jako nový stav pro tuto session.
    4.  **Spusť Posádku 2 a 3 (Paralelně, pokud je to možné):**
        *   **Posádka 2 (Vizuál):** Spusť posádku s agentem `canvas_architect_v1` a úkolem `task_format_canvas_output`. Jako vstup jí předej `updated_brief_state`.
        *   **Posádka 3 (Mluvčí):** Spusť posádku s agentem `interface_agent_v1` a úkolem `task_format_chat_response`. Jako vstup jí předej `suggested_instruction` a `chat_history`.
    5.  **Zkompletuj Finální Odpověď:** Počkej na výsledky od Posádky 2 a 3. Sestav finální JSON odpověď pro frontend, která bude obsahovat jak `chat_response`, tak `canvas_content`.
    6.  Ulož nový stav (`updated_brief_state` a aktualizovanou `chat_history`) zpět do Supabase pod správným `session_id`.

**Cílový výstup z API endpointu `/chat`:**
```json
{
  "session_id": "...",
  "chat_response": "Text pro levý panel chatu...",
  "canvas_content": "Markdown pro pravý panel plátna...",
  "is_complete": false
}
```

Tímto zadáním dáváte kódérovi jasnou a kompletní vizi pro přechod na naši novou, mnohem sofistikovanější architekturu. Je to velký krok, ale přesně ten, který Matyldu posune na "geniální" úroveň.