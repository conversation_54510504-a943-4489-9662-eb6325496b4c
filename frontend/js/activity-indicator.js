/**
 * Activity Indicator - Real-time agent activity podobné ChatGPT reasoning módu
 * Zobrazuje probíhající aktivity agentů v chat rozhraní
 */

class ActivityIndicator {
    constructor() {
        this.activeActivities = new Map();
        this.activityContainer = null;
        this.isVisible = false;
        
        this.init();
    }
    
    init() {
        console.log('🤖 Activity Indicator inicializován');
        this.createActivityContainer();
    }
    
    createActivityContainer() {
        // Vytvoření kontejneru pro aktivity přímo v chat messages
        const chatMessages = document.getElementById('chatMessages');
        if (!chatMessages) {
            console.error('❌ Chat messages nenalezeny');
            return;
        }

        // Activity se zobrazí jako speciální zpráva v chatu
        this.activityContainer = chatMessages;
        this.activityList = chatMessages;

        console.log('✅ Activity indicator nastaven pro inline zobrazení v chatu');
    }
    
    bindEvents() {
        const toggleBtn = document.getElementById('activity-toggle');
        if (toggleBtn) {
            toggleBtn.addEventListener('click', () => {
                this.toggleActivityDetails();
            });
        }
    }
    
    showActivity(agent, description, status = 'started', activityType = 'thinking') {
        if (!this.activityContainer) return;

        const activityId = `${agent}_${Date.now()}`;
        const activity = {
            id: activityId,
            agent: agent,
            description: description,
            status: status,
            activityType: activityType,
            startTime: new Date(),
            element: null
        };

        // Vytvoření chat zprávy pro aktivitu
        const activityElement = this.createChatActivityMessage(activity);
        activity.element = activityElement;

        // Přidání do mapy aktivních aktivit
        this.activeActivities.set(activityId, activity);

        // Přidání do chat messages
        this.activityContainer.appendChild(activityElement);

        // Scroll na konec chatu
        this.activityContainer.scrollTop = this.activityContainer.scrollHeight;

        console.log(`🤖 ${agent}: ${description} (${status})`);

        return activityId;
    }
    
    updateActivity(activityId, status, description = null) {
        const activity = this.activeActivities.get(activityId);
        if (!activity) return;
        
        activity.status = status;
        if (description) {
            activity.description = description;
        }
        
        // Aktualizace DOM elementu
        this.updateActivityElement(activity);
        
        // Pokud je aktivita dokončena, naplánuj její odstranění
        if (status === 'completed' || status === 'failed') {
            setTimeout(() => {
                this.removeActivity(activityId);
            }, 3000); // Odstranění po 3 sekundách
        }
    }
    
    removeActivity(activityId) {
        const activity = this.activeActivities.get(activityId);
        if (!activity) return;
        
        // Animace ven
        this.animateActivityOut(activity.element, () => {
            // Odstranění z DOM
            if (activity.element && activity.element.parentNode) {
                activity.element.parentNode.removeChild(activity.element);
            }
            
            // Odstranění z mapy
            this.activeActivities.delete(activityId);
            
            // Skrytí kontejneru pokud nejsou žádné aktivity
            if (this.activeActivities.size === 0) {
                this.hideActivityContainer();
            }
        });
    }
    
    createChatActivityMessage(activity) {
        const element = document.createElement('div');
        element.className = 'message assistant activity-message';
        element.dataset.activityId = activity.id;

        const statusIcon = this.getStatusIcon(activity.status);
        const typeIcon = this.getActivityTypeIcon(activity.activityType);
        const agentName = this.getAgentDisplayName(activity.agent);

        element.innerHTML = `
            <div class="message-content">
                <div class="activity-header">
                    <span class="activity-agent">
                        <span class="agent-icon">${typeIcon}</span>
                        <span class="agent-name">${agentName}</span>
                    </span>
                    <span class="activity-status">
                        <span class="status-icon">${statusIcon}</span>
                        <span class="activity-time">${this.formatTime(activity.startTime)}</span>
                    </span>
                </div>
                <div class="activity-description">${activity.description}</div>
                <div class="activity-progress">
                    <div class="progress-bar ${activity.status}"></div>
                </div>
            </div>
        `;

        return element;
    }

    createActivityElement(activity) {
        // Fallback pro starý systém
        return this.createChatActivityMessage(activity);
    }
    
    updateActivityElement(activity) {
        if (!activity.element) return;
        
        const statusIcon = this.getStatusIcon(activity.status);
        const statusIconElement = activity.element.querySelector('.status-icon');
        const descriptionElement = activity.element.querySelector('.activity-description');
        const progressBar = activity.element.querySelector('.progress-bar');
        
        if (statusIconElement) {
            statusIconElement.textContent = statusIcon;
        }
        
        if (descriptionElement) {
            descriptionElement.textContent = activity.description;
        }
        
        if (progressBar) {
            progressBar.className = `progress-bar ${activity.status}`;
        }
        
        // Aktualizace CSS třídy pro styling
        activity.element.className = `activity-item ${activity.status}`;
    }
    
    showActivityContainer() {
        if (this.activityContainer && !this.isVisible) {
            this.activityContainer.style.display = 'block';
            this.isVisible = true;
            
            // Animace zobrazení
            requestAnimationFrame(() => {
                this.activityContainer.classList.add('visible');
            });
        }
    }
    
    hideActivityContainer() {
        if (this.activityContainer && this.isVisible) {
            this.activityContainer.classList.remove('visible');
            this.isVisible = false;
            
            // Skrytí po animaci
            setTimeout(() => {
                if (this.activeActivities.size === 0) {
                    this.activityContainer.style.display = 'none';
                }
            }, 300);
        }
    }
    
    toggleActivityDetails() {
        if (!this.activityList) return;
        
        const isExpanded = this.activityList.classList.contains('expanded');
        const toggleIcon = document.querySelector('.toggle-icon');
        
        if (isExpanded) {
            this.activityList.classList.remove('expanded');
            if (toggleIcon) toggleIcon.textContent = '▼';
        } else {
            this.activityList.classList.add('expanded');
            if (toggleIcon) toggleIcon.textContent = '▲';
        }
    }
    
    animateActivityIn(element) {
        element.style.opacity = '0';
        element.style.transform = 'translateY(-10px)';
        
        requestAnimationFrame(() => {
            element.style.transition = 'all 0.3s ease';
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        });
    }
    
    animateActivityOut(element, callback) {
        element.style.transition = 'all 0.3s ease';
        element.style.opacity = '0';
        element.style.transform = 'translateY(-10px)';
        
        setTimeout(callback, 300);
    }
    
    // Utility funkce
    getStatusIcon(status) {
        const icons = {
            'started': '🚀',
            'in_progress': '⏳',
            'completed': '✅',
            'failed': '❌',
            'paused': '⏸️'
        };
        return icons[status] || '❓';
    }
    
    getActivityTypeIcon(type) {
        const icons = {
            'thinking': '🧠',
            'researching': '🔍',
            'writing': '✍️',
            'updating': '🔄',
            'reviewing': '👀',
            'waiting': '⏳'
        };
        return icons[type] || '🤖';
    }
    
    getAgentDisplayName(agentId) {
        const names = {
            'research_strategist_v1': 'Research Strategist',
            'communication_expert_v1': 'Communication Expert',
            'data_architect_v1': 'Data Architect',
            'alfa_orchestrator': 'Orchestrator'
        };
        return names[agentId] || agentId;
    }
    
    formatTime(date) {
        return date.toLocaleTimeString('cs-CZ', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }
    
    // Veřejné API pro integraci s canvas managerem
    updateFromCanvasActivities(activities) {
        // Synchronizace s aktivitami z canvas manageru
        const currentIds = new Set(this.activeActivities.keys());
        const newIds = new Set();
        
        activities.forEach(activity => {
            const id = `${activity.agent_id}_${activity.timestamp}`;
            newIds.add(id);
            
            if (!currentIds.has(id)) {
                // Nová aktivita
                this.showActivity(
                    activity.agent_id,
                    activity.description,
                    activity.status,
                    activity.activity_type
                );
            } else {
                // Aktualizace existující aktivity
                this.updateActivity(id, activity.status, activity.description);
            }
        });
        
        // Odstranění aktivit, které už nejsou v seznamu
        currentIds.forEach(id => {
            if (!newIds.has(id)) {
                this.removeActivity(id);
            }
        });
    }
    
    clearAllActivities() {
        this.activeActivities.forEach((activity, id) => {
            this.removeActivity(id);
        });
    }
}

// Globální instance
window.activityIndicator = new ActivityIndicator();
