#!/usr/bin/env python3
import yaml

try:
    with open('backend/tasks/alfa_tasks.yaml', 'r', encoding='utf-8') as f:
        data = yaml.safe_load(f)
    print('✅ YAML je validní')
    # <PERSON><PERSON>ly jsou přímo v root objektu, ne pod klíčem "tasks"
    tasks = {k: v for k, v in data.items() if isinstance(v, dict) and not k.startswith('#')}
    print(f'Počet úkolů: {len(tasks)}')
    for task_name in tasks:
        print(f'  - {task_name}')
except Exception as e:
    print(f'❌ YAML chyba: {e}')
