#!/bin/bash

# Start script pro Matylda API Gateway
# Spouští gateway server na portu 8002

echo "🚀 Spouštím Matylda API Gateway..."

# Kontrola virtuálního prostředí
if [ ! -d "venv" ]; then
    echo "❌ Virtuální prostředí nenalezeno. Spusťte nejprve setup.sh"
    exit 1
fi

# Aktivace virtuálního prostředí
source venv/bin/activate

# Kontrola závislostí
echo "📦 Kontrola závislostí..."
pip install -q uvicorn fastapi

# Vytvoření logs adresáře
mkdir -p logs

# Spuštění gateway serveru
echo "🌐 Spouštím API Gateway na portu 8002..."
cd backend

# Spuštění s uvicorn
uvicorn api_gateway:gateway_app --host 0.0.0.0 --port 8002 --reload --log-level info > ../logs/gateway.log 2>&1 &

GATEWAY_PID=$!
echo $GATEWAY_PID > ../logs/gateway.pid

echo "✅ API Gateway spuštěn na portu 8002 (PID: $GATEWAY_PID)"
echo "📊 Swagger UI: http://localhost:8002/docs"
echo "🏥 Health check: http://localhost:8002/gateway/health"
echo "📝 Logy: logs/gateway.log"

# Čekání na spuštění
sleep 3

# Test dostupnosti
if curl -s http://localhost:8002/gateway/health > /dev/null; then
    echo "✅ Gateway je dostupný"
else
    echo "❌ Gateway není dostupný"
    exit 1
fi

echo "🎉 Matylda API Gateway úspěšně spuštěn!"
