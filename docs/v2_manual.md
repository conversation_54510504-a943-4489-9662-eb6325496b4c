# **Matylda v2.0: Uživatelská a Administrátorská Příručka**

## **1. Úvod: Co je Matylda?**

**Matylda** není jen chatbot – je to **univerzální platforma pro tvorbu specializovaných AI konzultantů**. <PERSON><PERSON><PERSON> misí je posunout paradigma od reaktivního "objednávání dotazníků" k **proaktivnímu, daty podložen<PERSON>mu strategickému partnerství**.

Matylda ztělesňuje **"Model Geniálního Konzultanta"** – každý agent v ekosystému disponuje hlubokými znalostmi, ovládá konkrétní nástroje, projevuje autonomii, komunikuje empaticky a efektivně spolupracuje v týmu.

### Klíčové Vlastnosti v2.0

- **Univerzální Platforma:** Podporuje neomezený počet specializovaných domén
- **Mikroservisní Architektura:** Škálovatelná a modulární struktura
- **<PERSON>ým <PERSON>ů:** Každá doména má své specializované agenty
- **API Gateway:** Centrální routing pro všechny služby
- **Izolované Znalostní Báze:** Každá doména má vlastní znalosti
- **Duální Rozhraní:** Dialog + Canvas pro komplexní interakci

## **2. Klíčové Koncepty a Terminologie**

### **Doména (Domain)**
Specializovaná oblast znalostí s vlastní konfigurací, agenty a znalostní bází. Příklady:
- **"Onboarding Průzkumů"** - Příprava a plánování výzkumných projektů
- **"Sales Qualification"** - Kvalifikace obchodních příležitostí
- **"Participativní Rozpočet"** - Facilitace občanské participace
- **"Customer Support"** - Inteligentní zákaznická podpora

Každá doména je kompletně izolovaná s vlastními:
- Znalostními bázemi v Supabase
- Specializovanými agenty
- Best practices databází
- Konfiguračními soubory

### **Agent**
Digitální specialista s definovanou rolí, osobností a cílem. V Matylda v2.0 máme tři typy agentů:

- **Strategický Onboarder** - Logický a strategický motor, analyzuje požadavky
- **Komunikační Specialista** - Empatický komunikátor, formuluje odpovědi
- **Canvas Architekt** - Vizuální syntetizátor, vytváří strukturované výstupy

Agenti jsou definováni v `backend/agents/specialist_agents.yaml` s vlastnostmi:
```yaml
strategic_onboarder_v1:
  role: "Logický a Strategický Motor Onboardingu"
  goal: "Analyzovat stav briefu a rozhodnout o dalším kroku"
  backstory: "Jsi analytický expert..."
```

### **Nástroj (Tool)**
Konkrétní dovednost, kterou agent může použít:
- **`knowledge_base_search`** - Prohledávání znalostní báze
- **`best_practices_search`** - Vyhledávání osvědčených postupů
- **`project_analysis`** - Analýza projektových dat

### **Úkol (Task)**
Konkrétní zadání pro agenta definované v `backend/tasks/specialist_tasks.yaml`:
```yaml
strategic_analysis_task:
  description: "Analyzuj historii chatu a aktuální stav briefu"
  expected_output: "JSON s next_action a updated_brief_state"
```

### **Orchestrátor**
Centrální řídící systém, který:
- Určuje správnou doménu na základě požadavku
- Sestavuje tým agentů pro danou doménu
- Koordinuje spolupráci mezi agenty
- Poskytuje fallback mechanismy

### **Plátno (Canvas)**
Vizuální komponenta duálního rozhraní:
- **Dialog** - Konverzace s uživatelem
- **Canvas** - Strukturovaný přehled průběžných výsledků v Markdown formátu

### **API Gateway**
Centrální vstupní bod pro všechny služby:
- **Chat** - Konverzační služba
- **Knowledge** - Vyhledávání ve znalostních bázích
- **Best Practices** - Osvědčené postupy
- **Analytics** - Statistiky a monitoring
- **Admin** - Administrační funkce

## **3. Praktický Průvodce: Jak Přidat Novou Doménu**

Přidání nové domény **"Technické Poradenství"** krok za krokem:

### **Krok 1: Vytvoření Znalostní Báze**

#### 1.1 Supabase Tabulky
Vytvořte nové tabulky v Supabase podle vzoru v `backend/sql/domain_knowledge_bases.sql`:

```sql
-- Tabulka pro technické znalosti
CREATE TABLE IF NOT EXISTS kb_technicke_poradenstvi (
    id BIGSERIAL PRIMARY KEY,
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    embedding VECTOR(1536),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Vyhledávací funkce
CREATE OR REPLACE FUNCTION match_technicke_poradenstvi(
  query_embedding VECTOR(1536),
  match_threshold FLOAT DEFAULT 0.78,
  match_count INT DEFAULT 5
)
RETURNS TABLE (
  id BIGINT,
  content TEXT,
  metadata JSONB,
  similarity FLOAT
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    kb_technicke_poradenstvi.id,
    kb_technicke_poradenstvi.content,
    kb_technicke_poradenstvi.metadata,
    1 - (kb_technicke_poradenstvi.embedding <=> query_embedding) AS similarity
  FROM kb_technicke_poradenstvi
  WHERE 1 - (kb_technicke_poradenstvi.embedding <=> query_embedding) > match_threshold
  ORDER BY kb_technicke_poradenstvi.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;
```

#### 1.2 Naplnění Dat
Nahrajte relevantní dokumenty do tabulky pomocí embedding procesu.

### **Krok 2: Konfigurace Domény**

#### 2.1 Přidání do domains.yaml
Upravte `backend/config/domains.yaml`:

```yaml
domains:
  technicke_poradenstvi:
    name: "Technické Poradenství"
    description: "Specializované technické poradenství a konzultace"
    enabled: true
    keywords:
      - "technické"
      - "poradenství"
      - "konzultace"
      - "implementace"
    orchestrator_config:
      specialist_agents_file: "specialist_agents.yaml"
      specialist_tasks_file: "specialist_tasks.yaml"
```

#### 2.2 Konfigurace Znalostních Bází
Přidejte do `backend/config/domain_knowledge_bases.yaml`:

```yaml
technicke_poradenstvi:
  knowledge_bases:
    - id: "technicke_znalosti"
      table_name: "kb_technicke_poradenstvi"
      query_function: "match_technicke_poradenstvi"
      description: "Technické znalosti a postupy"
      categories:
        - "technologie"
        - "implementace"
        - "best_practices"
```

### **Krok 3: Aktivace Domény**

#### 3.1 Restart Systému
Po konfiguraci restartujte všechny servery:
```bash
./stop_servers.sh
./start_servers.sh
```

#### 3.2 Ověření
Zkontrolujte dostupnost nové domény:
```bash
curl -X GET "http://localhost:8001/health"
```

### **Krok 4: Testování**

#### 4.1 Test Chat Služby
```bash
curl -X POST "http://localhost:8002/gateway/chat" \
  -H "Content-Type: application/json" \
  -d '{"message": "Potřebuji technické poradenství", "request_type": "technické"}'
```

#### 4.2 Test Knowledge Služby
```bash
curl -X POST "http://localhost:8002/gateway/knowledge" \
  -H "Content-Type: application/json" \
  -d '{"message": "implementační postupy", "domain": "technicke_poradenstvi"}'
```

## **4. Konfigurace Aplikace (.env)**

Klíčové proměnné prostředí:

### **Základní Konfigurace**
```env
# API Konfigurace
API_HOST=0.0.0.0
API_PORT=8001
API_RELOAD=True

# LLM Modely
DEFAULT_LLM_MODEL=gpt-4o-mini
CONVERSATIONAL_MODEL=gpt-4o-mini
CONVERSATIONAL_TEMPERATURE=0.3

# Supabase
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key
SUPABASE_TABLE_NAME=gdanalyst
SUPABASE_QUERY_NAME=match_gdanalyst

# OpenAI
OPENAI_API_KEY=your_openai_key

# Session Management
MAX_ACTIVE_SESSIONS=100
SESSION_TIMEOUT_MINUTES=60
```

### **Pokročilá Konfigurace**
```env
# Multi-Domain RAG
ENABLE_MULTI_DOMAIN_RAG=True
DEFAULT_SIMILARITY_THRESHOLD=0.78
MAX_KNOWLEDGE_RESULTS=5

# Best Practices
ENABLE_BEST_PRACTICES=True
BEST_PRACTICES_FILE=data/best_practices.json

# Orchestrator
ENABLE_MATYLDA_ORCHESTRATOR=True
DEFAULT_DOMAIN=onboarding_pruzkumy
```

## **5. API Dokumentace**

### **Hlavní Endpointy**

#### **Backend API (Port 8001)**
- **Swagger UI:** `http://localhost:8001/docs`
- **Health Check:** `http://localhost:8001/health`
- **Universal Chat:** `POST /chat/universal`

#### **API Gateway (Port 8002)**
- **Swagger UI:** `http://localhost:8002/docs`
- **Health Check:** `http://localhost:8002/gateway/health`
- **Services List:** `GET /gateway/services`

#### **Gateway Services**
- **Chat:** `POST /gateway/chat`
- **Knowledge:** `POST /gateway/knowledge`
- **Best Practices:** `POST /gateway/best_practices`
- **Analytics:** `POST /gateway/analytics`
- **Admin:** `POST /gateway/admin`

### **Frontend (Port 8082)**
- **Aplikace:** `http://localhost:8082`
- **Duální rozhraní:** Dialog + Canvas

### **Spuštění Systému**
```bash
# Spuštění všech služeb
./start_servers.sh

# Spuštění pouze gateway
./start_gateway.sh

# Zastavení všech služeb
./stop_servers.sh
```

Detailní technická dokumentace všech API endpointů je k dispozici po spuštění serverů na příslušných `/docs` adresách.
