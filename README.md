# Projekt Matylda v2.0 - Univerzální AI Platforma

**Univerzální škálovatelná platforma** pro strategické AI partnerství napříč neomezeným počtem business domén. Postavená na mikroservisní architektuře s API Gateway, multi-domain RAG systémem a týmy specializovaných agentů.

## 🌟 Klíčové Vlastnosti v2.0

- **🚀 Univerzální Škálovatelnost** - Přidání nové domény během 30 minut
- **🏗️ Mikroservisní Architektura** - API Gateway + 5 nezávislých služeb
- **🧠 Tým Specialistů** - 3 specializované agenty pro každou doménu
- **📚 Multi-Domain RAG** - 15 izolovaných znalostních bází
- **🎯 Domain-Aware Best Practices** - Osvědčené postupy podle kontextu
- **📊 Duální Rozhraní** - Dialog + Canvas pro komplexní interakci

## 🏗️ Architektura v2.0

### Mikroservisy
- **🌐 API Gateway** (Port 8002) - Centrální routing pro všechny služby
- **🧠 Backend API** (Port 8001) - Hlavní business logika
- **💻 Frontend** (Port 8082) - Moderní webové rozhraní

### Služby přes API Gateway
- **💬 Chat** - Konverzační služba s týmy specialistů
- **📚 Knowledge** - Vyhledávání ve znalostních bázích
- **⭐ Best Practices** - Domain-aware osvědčené postupy
- **📊 Analytics** - Statistiky a monitoring
- **⚙️ Admin** - Administrační funkce

### Současné Domény
- ✅ **Onboarding Průzkumů** - Aktivní
- 🔄 **Sales Qualification** - Připraveno
- 🔄 **Participativní Rozpočet** - Připraveno
- 🔄 **Customer Support** - Připraveno

## 🚀 Rychlý Start

### 1. Příprava prostředí

```bash
# Klonování a přechod do adresáře
git clone <repository-url>
cd MATYLDA

# Vytvoření virtuálního prostředí
python -m venv venv
source venv/bin/activate  # Linux/Mac
# nebo
venv\Scripts\activate     # Windows

# Instalace závislostí
pip install -r requirements.txt
```

### 2. Konfigurace

Vytvořte `.env` soubor v root adresáři podle `.env.example`:

```env
# OpenAI API klíč (povinný)
OPENAI_API_KEY="your-openai-api-key"

# Supabase konfigurace (povinné pro RAG)
SUPABASE_URL="your-supabase-url"
SUPABASE_KEY="your-supabase-key"

# API Server nastavení
API_HOST=0.0.0.0
API_PORT=8001
API_RELOAD=True

# Multi-Domain RAG
ENABLE_MULTI_DOMAIN_RAG=True
DEFAULT_SIMILARITY_THRESHOLD=0.78

# Orchestrator
ENABLE_MATYLDA_ORCHESTRATOR=True
DEFAULT_DOMAIN=onboarding_pruzkumy
```

### 3. Spuštění systému

#### 🚀 Automatické spuštění (doporučeno)

```bash
# Spuštění všech služeb (Backend + Frontend)
./start_servers.sh

# Spuštění API Gateway (volitelně)
./start_gateway.sh
```

Tento skript automaticky:
- ✅ Načte konfiguraci z `.env` souboru
- ✅ Spustí Backend API server (Port 8001)
- ✅ Spustí Frontend HTTP server (Port 8082)
- ✅ Ověří, že oba servery běží správně
- ✅ **Automaticky otevře prohlížeč**
- ✅ Zobrazí přístupové URL

#### 🌐 API Gateway (Volitelné)

Pro pokročilé použití s mikroservisy:

```bash
# Spuštění API Gateway
./start_gateway.sh
```

API Gateway poskytuje:
- 🔀 **Centrální routing** pro všechny služby
- 📊 **Health monitoring** všech komponent
- 🛡️ **Error handling** a fallback mechanismy
- 📈 **Analytics** a statistiky

#### 🛑 Ukončení serverů

```bash
# Ukončení všech serverů
./stop_servers.sh
```

### 4. Přístupové body

Po spuštění budou dostupné tyto služby:

#### Hlavní Služby
| Služba | URL | Popis |
|--------|-----|-------|
| **🌐 Frontend** | http://localhost:8082 | Duální chat rozhraní (Dialog + Canvas) |
| **🔧 Backend API** | http://localhost:8001 | REST API pro konverzace |
| **🌐 API Gateway** | http://localhost:8002 | Mikroservisní routing |

#### API Dokumentace
| Služba | URL | Popis |
|--------|-----|-------|
| **📖 Backend Docs** | http://localhost:8001/docs | Swagger UI pro Backend API |
| **📖 Gateway Docs** | http://localhost:8002/docs | Swagger UI pro API Gateway |

#### Health Checks
| Služba | URL | Popis |
|--------|-----|-------|
| **🏥 Backend Health** | http://localhost:8001/health | Status Backend API |
| **🏥 Gateway Health** | http://localhost:8002/gateway/health | Status všech mikroservisů |

#### Gateway Služby
| Služba | Endpoint | Popis |
|--------|----------|-------|
| **💬 Chat** | POST /gateway/chat | Konverzační služba |
| **📚 Knowledge** | POST /gateway/knowledge | Vyhledávání ve znalostních bázích |
| **⭐ Best Practices** | POST /gateway/best_practices | Osvědčené postupy |
| **📊 Analytics** | POST /gateway/analytics | Statistiky a monitoring |
| **⚙️ Admin** | POST /gateway/admin | Administrační funkce |

## 📁 Struktura projektu v2.0

```
MATYLDA/
├── backend/                           # Python backend
│   ├── api_server.py                 # FastAPI server (hlavní API)
│   ├── api_gateway.py                # API Gateway pro mikroservisy
│   ├── matylda_orchestrator.py       # Univerzální orchestrátor
│   ├── specialist_orchestrator.py    # Orchestrátor týmu specialistů
│   ├── multi_domain_rag.py          # Multi-domain RAG systém
│   ├── best_practices_system.py     # Domain-aware best practices
│   ├── session_handler.py           # Session management
│   ├── config/                      # Konfigurace
│   │   ├── domains.yaml             # Definice domén
│   │   └── domain_knowledge_bases.yaml # Znalostní báze
│   ├── agents/                      # Definice agentů
│   │   └── specialist_agents.yaml   # Specializované agenty
│   ├── tasks/                       # Definice úkolů
│   │   └── specialist_tasks.yaml    # Úkoly pro specialisty
│   ├── sql/                         # SQL skripty pro Supabase
│   │   ├── domain_knowledge_bases.sql
│   │   └── domain_best_practices.sql
│   └── requirements.txt             # Python závislosti
├── frontend/                         # Webové rozhraní
│   ├── index.html                   # Duální rozhraní (Dialog + Canvas)
│   ├── style.css                    # Moderní styly
│   ├── chat.js                      # JavaScript logika
│   └── README.md                    # Frontend dokumentace
├── docs/                            # Dokumentace
│   └── v2_manual.md                # Uživatelská příručka v2.0
├── logs/                            # Logy serverů
├── .env                             # Konfigurace (vytvořte)
├── .env.example                     # Vzor konfigurace
├── start_servers.sh                 # Spuštění všech služeb
├── start_gateway.sh                 # Spuštění API Gateway
├── stop_servers.sh                  # Ukončení všech služeb
├── VISION.md                        # Vize projektu v2.0
├── ARCHITECTURE.md                  # Architektura v2.0
└── README.md                        # Tento soubor
```

## 🔧 Vývoj v2.0

### Přidání Nové Domény

Detailní návod najdete v [`docs/v2_manual.md`](docs/v2_manual.md).

Rychlý přehled:
```bash
# 1. Přidání do domains.yaml
# 2. Konfigurace znalostních bází
# 3. SQL skripty v Supabase
# 4. Restart systému
./stop_servers.sh && ./start_servers.sh
```

### Testování Komponent

```bash
cd backend

# Test Orchestrátoru
python matylda_orchestrator.py

# Test Multi-Domain RAG
python multi_domain_rag.py

# Test Best Practices
python best_practices_system.py

# Test API Gateway
python api_gateway.py
```

### Frontend

Frontend je moderní SPA s duálním rozhraním (Dialog + Canvas) postavené na vanilla JavaScript.

## 📖 Dokumentace v2.0

### Hlavní Dokumenty
- **[VISION.md](VISION.md)** - Vize univerzální platformy v2.0
- **[ARCHITECTURE.md](ARCHITECTURE.md)** - Mikroservisní architektura v2.0
- **[docs/v2_manual.md](docs/v2_manual.md)** - Uživatelská příručka v2.0

### Technická Dokumentace
- **[SCALABILITY_IMPLEMENTATION_SUMMARY.md](SCALABILITY_IMPLEMENTATION_SUMMARY.md)** - Shrnutí škálovatelnosti
- **[backend/sql/](backend/sql/)** - SQL skripty pro Supabase
- **[backend/config/](backend/config/)** - YAML konfigurace domén

## 🧪 Testování v2.0

### Backend API Test
```bash
# Health check
curl http://localhost:8001/health

# Universal chat endpoint
curl -X POST http://localhost:8001/chat/universal \
  -H "Content-Type: application/json" \
  -d '{"message": "Potřebuji udělat průzkum spokojenosti", "request_type": "průzkum"}'
```

### API Gateway Test
```bash
# Gateway health
curl http://localhost:8002/gateway/health

# Chat služba
curl -X POST http://localhost:8002/gateway/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Potřebuji kvalifikovat klienta", "request_type": "sales"}'

# Knowledge služba
curl -X POST http://localhost:8002/gateway/knowledge \
  -H "Content-Type: application/json" \
  -d '{"message": "metodologie průzkumů", "domain": "onboarding_pruzkumy"}'

# Best practices služba
curl -X POST http://localhost:8002/gateway/best_practices \
  -H "Content-Type: application/json" \
  -d '{"message": "komunikace", "domain": "onboarding_pruzkumy", "context": "komunikace"}'

# Analytics služba
curl -X POST http://localhost:8002/gateway/analytics \
  -H "Content-Type: application/json" \
  -d '{"message": "domain statistics", "domain": "onboarding_pruzkumy"}'
```

### Frontend Test
Otevřete http://localhost:8082 a vyzkoušejte duální rozhraní (Dialog + Canvas).

## 🚀 Produkční nasazení v2.0

### Požadavky
- **Python 3.8+**
- **OpenAI API klíč**
- **Supabase instance** s vector extension
- **Porty:** 8001 (Backend), 8002 (Gateway), 8082 (Frontend)

### Škálovatelnost
- **Horizontální škálování:** Každá služba může běžet nezávisle
- **Load balancing:** API Gateway podporuje distribuci zátěže
- **Database:** Supabase automaticky škáluje podle potřeby
- **Monitoring:** Health checks pro všechny komponenty

### Docker Deployment (připraveno)
```bash
# Celý stack
docker-compose up -d

# Jednotlivé služby
docker run -p 8001:8001 matylda-backend
docker run -p 8002:8002 matylda-gateway
docker run -p 8082:8082 matylda-frontend
```

## 🔍 Troubleshooting v2.0

### Časté problémy

1. **Backend se nespustí**
   - Zkontrolujte OpenAI API klíč v `.env`
   - Ověřte Supabase připojení
   - Zkontrolujte, že porty 8001, 8002, 8082 nejsou obsazené

2. **API Gateway nedostupný**
   - Spusťte `./start_gateway.sh`
   - Zkontrolujte logy: `tail -f logs/gateway.log`

3. **Znalostní báze nefungují**
   - Spusťte SQL skripty v Supabase
   - Zkontrolujte `backend/sql/domain_knowledge_bases.sql`

4. **Nová doména se nezobrazuje**
   - Zkontrolujte `backend/config/domains.yaml`
   - Restartujte servery: `./stop_servers.sh && ./start_servers.sh`

### Logy
```bash
# Backend logy
tail -f logs/backend.log

# Gateway logy
tail -f logs/gateway.log

# Frontend logy
tail -f logs/frontend.log
```

## 🎯 Co je nového v2.0?

- ✅ **Univerzální škálovatelnost** - Neomezený počet domén
- ✅ **Mikroservisní architektura** - API Gateway + 5 služeb
- ✅ **Multi-domain RAG** - 15 izolovaných znalostních bází
- ✅ **Tým specialistů** - 3 agenti pro každou doménu
- ✅ **Domain-aware best practices** - Kontextové osvědčené postupy
- ✅ **Duální rozhraní** - Dialog + Canvas
- ✅ **Production-ready** - Kompletní monitoring

Matylda v2.0 je připravena stát se skutečně univerzální platformou pro strategické AI partnerství! 🚀

2. **Frontend se nepřipojí k backend**
   - Zkontrolujte, že backend běží na portu 8001
   - Ověřte CORS nastavení

3. **RAG systém nefunguje**
   - Zkontrolujte Supabase konfiguraci
   - RAG je volitelný, systém funguje i bez něj

### Logy a debugging

```bash
# Backend s debug logováním
cd backend
LOG_LEVEL=DEBUG python api_server.py

# Frontend debugging
# Otevřete Developer Tools v prohlížeči (F12)
```

## 🤝 Přispívání

1. Forkněte repository
2. Vytvořte feature branch (`git checkout -b feature/nova-funkcionalita`)
3. Commitněte změny (`git commit -am 'Přidání nové funkcionality'`)
4. Pushněte branch (`git push origin feature/nova-funkcionalita`)
5. Vytvořte Pull Request

## 📄 Licence

Tento projekt je licencován pod [MIT licencí](LICENSE).

## 📞 Kontakt

Pro otázky a podporu kontaktujte vývojový tým nebo vytvořte issue v tomto repository.