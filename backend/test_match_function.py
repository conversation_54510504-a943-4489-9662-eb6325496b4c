#!/usr/bin/env python3
"""
Test funkce match_best_practices s r<PERSON><PERSON><PERSON><PERSON><PERSON> způsoby volání
"""

import os
from supabase import create_client, Client
from dotenv import load_dotenv

load_dotenv()

def test_match_function():
    """Test různých způsobů volání match_best_practices"""
    
    url = os.getenv('SUPABASE_URL')
    key = os.getenv('SUPABASE_KEY')
    
    supabase: Client = create_client(url, key)
    print("✅ Připojen k Supabase")
    
    # Dummy embedding pro test
    dummy_embedding = [0.1] * 1536
    
    # Test 1: <PERSON><PERSON><PERSON>dn<PERSON> způsob volání
    print("\n🔧 Test 1: <PERSON><PERSON><PERSON><PERSON><PERSON> způsob volání...")
    try:
        result = supabase.rpc('match_best_practices', {
            'query_embedding': dummy_embedding,
            'match_threshold': 0.5,
            'match_count': 5
        }).execute()
        print(f"✅ Pů<PERSON><PERSON>í způsob funguje: {len(result.data)} výsledků")
    except Exception as e:
        print(f"❌ Původní způsob: {e}")
    
    # Test 2: <PERSON><PERSON> pořadí parametrů
    print("\n🔧 Test 2: Jiné pořadí parametrů...")
    try:
        result = supabase.rpc('match_best_practices', {
            'match_threshold': 0.5,
            'match_count': 5,
            'query_embedding': dummy_embedding
        }).execute()
        print(f"✅ Jiné pořadí funguje: {len(result.data)} výsledků")
    except Exception as e:
        print(f"❌ Jiné pořadí: {e}")
    
    # Test 3: Bez některých parametrů
    print("\n🔧 Test 3: Pouze s embedding...")
    try:
        result = supabase.rpc('match_best_practices', {
            'query_embedding': dummy_embedding
        }).execute()
        print(f"✅ Pouze embedding funguje: {len(result.data)} výsledků")
    except Exception as e:
        print(f"❌ Pouze embedding: {e}")
    
    # Test 4: Kratší embedding
    print("\n🔧 Test 4: Kratší embedding...")
    try:
        short_embedding = [0.1] * 100
        result = supabase.rpc('match_best_practices', {
            'query_embedding': short_embedding,
            'match_threshold': 0.5,
            'match_count': 5
        }).execute()
        print(f"✅ Kratší embedding funguje: {len(result.data)} výsledků")
    except Exception as e:
        print(f"❌ Kratší embedding: {e}")
    
    # Test 5: Bez embeddingu (jen textové parametry)
    print("\n🔧 Test 5: Bez embeddingu...")
    try:
        result = supabase.rpc('match_best_practices', {
            'match_threshold': 0.5,
            'match_count': 5
        }).execute()
        print(f"✅ Bez embeddingu funguje: {len(result.data)} výsledků")
    except Exception as e:
        print(f"❌ Bez embeddingu: {e}")
    
    # Test 6: Zkusíme zjistit, jaké funkce jsou dostupné
    print("\n🔧 Test 6: Dostupné funkce...")
    try:
        # Pokus o volání neexistující funkce pro porovnání
        result = supabase.rpc('nonexistent_function').execute()
    except Exception as e:
        print(f"Chyba neexistující funkce: {e}")
        
        # Zkusíme match_gdanalyst (která podle hintu existuje)
        try:
            result = supabase.rpc('match_gdanalyst', {
                'query_embedding': dummy_embedding,
                'match_threshold': 0.5,
                'match_count': 5
            }).execute()
            print(f"✅ match_gdanalyst funguje: {len(result.data)} výsledků")
        except Exception as e2:
            print(f"❌ match_gdanalyst: {e2}")
    
    print("\n🎉 Test dokončen!")

if __name__ == "__main__":
    print("🧪 Test funkce match_best_practices...")
    test_match_function()
