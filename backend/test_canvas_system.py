#!/usr/bin/env python3
"""
Komplexní test sdíleného canvas systému
Testuje všechny komponenty: Canvas, Activity Tracker, WebSocket, API
"""

import asyncio
import json
import time
import requests
import websockets
from datetime import datetime
import logging

# Nastavení logování
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CanvasSystemTester:
    def __init__(self):
        self.base_url = "http://localhost:8001"
        self.ws_url = "ws://localhost:8001"
        self.session_id = None
        self.websocket = None
        
    async def run_complete_test(self):
        """Spustí kompletní test celého systému"""
        print("🧪 === KOMPLEXNÍ TEST CANVAS SYSTÉMU ===")
        
        try:
            # Test 1: API Health Check
            await self.test_api_health()
            
            # Test 2: Vytvoření session přes chat
            await self.test_create_session()
            
            # Test 3: Canvas API endpointy
            await self.test_canvas_api()
            
            # Test 4: WebSocket připojení
            await self.test_websocket_connection()
            
            # Test 5: Real-time updates
            await self.test_realtime_updates()
            
            # Test 6: Performance test
            await self.test_performance()
            
            print("🎉 === VŠECHNY TESTY ÚSPĚŠNÉ ===")
            
        except Exception as e:
            print(f"❌ Test selhal: {e}")
            import traceback
            traceback.print_exc()
        finally:
            if self.websocket:
                await self.websocket.close()
    
    async def test_api_health(self):
        """Test API health check"""
        print("\n🔍 Test 1: API Health Check")
        
        response = requests.get(f"{self.base_url}/health")
        assert response.status_code == 200, f"Health check failed: {response.status_code}"
        
        data = response.json()
        assert data["status"] == "healthy", f"API není healthy: {data}"
        
        print("✅ API Health Check úspěšný")
    
    async def test_create_session(self):
        """Test vytvoření session přes chat API"""
        print("\n🔍 Test 2: Vytvoření Session")
        
        chat_data = {
            "message": "Chci udělat průzkum spokojenosti občanů v Praze 21",
            "domain": "research_public_opinion_municipal"
        }
        
        print("📤 Odesílám chat request...")
        response = requests.post(
            f"{self.base_url}/chat/universal",
            json=chat_data,
            timeout=60
        )
        
        assert response.status_code == 200, f"Chat request failed: {response.status_code}"
        
        data = response.json()
        assert "session_id" in data, "Session ID chybí v odpovědi"
        
        self.session_id = data["session_id"]
        print(f"✅ Session vytvořena: {self.session_id}")
        print(f"📝 Odpověď délka: {len(data.get('chat_response', ''))}")
    
    async def test_canvas_api(self):
        """Test Canvas API endpointů"""
        print("\n🔍 Test 3: Canvas API Endpointy")
        
        if not self.session_id:
            raise Exception("Session ID není nastaveno")
        
        # Test základního canvas endpointu
        print("📤 Test GET /canvas/{session_id}")
        response = requests.get(f"{self.base_url}/canvas/{self.session_id}")
        
        if response.status_code == 404:
            print("⚠️ Canvas ještě neexistuje - to je OK pro novou session")
        else:
            assert response.status_code == 200, f"Canvas API failed: {response.status_code}"
            data = response.json()
            assert "client_view" in data, "Client view chybí"
            print(f"✅ Canvas API - client view: {len(data['client_view'])} znaků")
        
        # Test debug endpointu
        print("📤 Test GET /canvas/{session_id}?debug=true")
        response = requests.get(f"{self.base_url}/canvas/{self.session_id}?debug=true")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Debug view: {len(data.get('debug_view', ''))} znaků")
        
        # Test activities endpointu
        print("📤 Test GET /canvas/{session_id}/activities")
        response = requests.get(f"{self.base_url}/canvas/{self.session_id}/activities")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Activities: {data.get('count', 0)} aktivit")
        
        print("✅ Canvas API testy dokončeny")
    
    async def test_websocket_connection(self):
        """Test WebSocket připojení"""
        print("\n🔍 Test 4: WebSocket Připojení")
        
        if not self.session_id:
            raise Exception("Session ID není nastaveno")
        
        ws_url = f"{self.ws_url}/ws/{self.session_id}"
        print(f"📤 Připojuji WebSocket: {ws_url}")
        
        try:
            self.websocket = await websockets.connect(ws_url)
            print("✅ WebSocket připojen")
            
            # Čekání na úvodní zprávu
            message = await asyncio.wait_for(self.websocket.recv(), timeout=5.0)
            data = json.loads(message)
            
            assert data["type"] == "connection_established", f"Neočekávaná zpráva: {data}"
            print("✅ Connection established zpráva přijata")
            
            # Test ping/pong
            await self.websocket.send(json.dumps({
                "type": "ping",
                "timestamp": datetime.now().isoformat()
            }))
            
            pong_message = await asyncio.wait_for(self.websocket.recv(), timeout=5.0)
            pong_data = json.loads(pong_message)
            
            assert pong_data["type"] == "pong", f"Neočekávaná pong odpověď: {pong_data}"
            print("✅ Ping/Pong test úspěšný")
            
        except Exception as e:
            print(f"❌ WebSocket test selhal: {e}")
            raise
    
    async def test_realtime_updates(self):
        """Test real-time updates přes WebSocket"""
        print("\n🔍 Test 5: Real-time Updates")
        
        if not self.websocket:
            raise Exception("WebSocket není připojen")
        
        # Request update
        await self.websocket.send(json.dumps({
            "type": "request_update",
            "session_id": self.session_id
        }))
        
        print("📤 Request update odeslán")
        
        # Čekání na canvas update
        try:
            update_message = await asyncio.wait_for(self.websocket.recv(), timeout=10.0)
            update_data = json.loads(update_message)
            
            if update_data["type"] == "canvas_update":
                print("✅ Canvas update přijat přes WebSocket")
                print(f"📊 Canvas views: {list(update_data.get('canvas_views', {}).keys())}")
                print(f"🤖 Activities: {len(update_data.get('activities', []))}")
            else:
                print(f"⚠️ Neočekávaný typ zprávy: {update_data['type']}")
                
        except asyncio.TimeoutError:
            print("⚠️ Canvas update nepřijat (timeout) - možná není canvas data")
        
        print("✅ Real-time updates test dokončen")
    
    async def test_performance(self):
        """Test výkonu systému"""
        print("\n🔍 Test 6: Performance Test")
        
        # Test rychlosti API odpovědí
        start_time = time.time()
        response = requests.get(f"{self.base_url}/health")
        api_time = time.time() - start_time
        
        print(f"⏱️ API response time: {api_time:.3f}s")
        assert api_time < 1.0, f"API příliš pomalé: {api_time}s"
        
        # Test WebSocket latence
        if self.websocket:
            start_time = time.time()
            await self.websocket.send(json.dumps({"type": "ping"}))
            await self.websocket.recv()
            ws_latency = time.time() - start_time
            
            print(f"⏱️ WebSocket latency: {ws_latency:.3f}s")
            assert ws_latency < 0.5, f"WebSocket příliš pomalý: {ws_latency}s"
        
        print("✅ Performance test úspěšný")
    
    def test_frontend_integration(self):
        """Test frontend integrace (manuální)"""
        print("\n🔍 Test 7: Frontend Integrace (Manuální)")
        print("📋 Checklist pro manuální test:")
        print("  1. Otevři http://localhost:8082")
        print("  2. Zkontroluj, že canvas panel je v pravém sloupci")
        print("  3. Pošli zprávu a sleduj:")
        print("     - Activity indicator v levém panelu")
        print("     - Canvas updates v pravém panelu")
        print("     - Debug toggle funkčnost")
        print("     - WebSocket status v footer")
        print("  4. Zkus debug mode toggle")
        print("  5. Zkontroluj real-time updates")

async def main():
    """Hlavní test funkce"""
    tester = CanvasSystemTester()
    await tester.run_complete_test()
    
    # Manuální frontend test
    tester.test_frontend_integration()
    
    print("\n🎉 === TESTOVÁNÍ DOKONČENO ===")
    print("📋 Výsledky:")
    print("  ✅ API Health Check")
    print("  ✅ Session Creation")
    print("  ✅ Canvas API Endpoints")
    print("  ✅ WebSocket Connection")
    print("  ✅ Real-time Updates")
    print("  ✅ Performance Test")
    print("  📋 Frontend Integration (manuální)")

if __name__ == "__main__":
    asyncio.run(main())
