# Konfigurace posádky pro specializované agenty
# Architektura "Týmu Specialistů" s třemi agenty

specialist_onboarding_crew:
  description: >
    Pokročilá posádka tří specializovaných agentů pro vysokou kvalitu onboardingu.
    Každý agent má specifickou roli a spolupracují v reálném čase.
    
  agents:
    - strategic_onboarder_v1    # <PERSON><PERSON><PERSON> rozho<PERSON>
    - interface_agent_v1        # Komunikace s klientem
    - canvas_architect_v1       # Vizualizace stavu
    
  tasks:
    - task_decide_next_step     # <PERSON>k<PERSON> rozhodnutí
    - task_format_chat_response # Komunikace
    - task_format_canvas_output # Vizualizace
    
  process: "sequential"  # Postupné zpracování
  verbose: true
  memory: false  # Izolovaná paměť pro každou session
  
  # Workflow popis:
  # 1. strategic_onboarder_v1 analyzuje situaci a rozhodne o dalším kroku
  # 2. interface_agent_v1 zformuluje odpověď pro klienta
  # 3. canvas_architect_v1 aktualizuje vizuální stav
  
  # Konfigurace pro škálovatelnost
  domain: "onboarding_pruzkumy"
  knowledge_bases:
    - "pruzkumy_municipality"
    - "best_practices_onboarding"
