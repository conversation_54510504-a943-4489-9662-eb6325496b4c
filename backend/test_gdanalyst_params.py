#!/usr/bin/env python3
"""
Test různých parametrů pro match_gdanalyst funkci
"""

import os
from supabase import create_client, Client
from dotenv import load_dotenv

load_dotenv()

def test_gdanalyst_params():
    """Test různých způsobů volání match_gdanalyst"""
    
    url = os.getenv('SUPABASE_URL')
    key = os.getenv('SUPABASE_KEY')
    
    supabase: Client = create_client(url, key)
    print("✅ Připojen k Supabase")
    
    # Dummy embedding pro test
    dummy_embedding = [0.1] * 1536
    
    # Test 1: Podle hintu (filter, match_count, query_embedding)
    print("\n🔧 Test 1: Podle hintu (filter, match_count, query_embedding)...")
    try:
        result = supabase.rpc('match_gdanalyst', {
            'filter': {},
            'match_count': 3,
            'query_embedding': dummy_embedding
        }).execute()
        print(f"✅ Hint parametry fungují: {len(result.data)} výsledků")
        if result.data:
            print(f"📊 Struktura: {list(result.data[0].keys())}")
    except Exception as e:
        print(f"❌ Hint parametry: {e}")
    
    # Test 2: Bez filter parametru
    print("\n🔧 Test 2: Bez filter parametru...")
    try:
        result = supabase.rpc('match_gdanalyst', {
            'match_count': 3,
            'query_embedding': dummy_embedding
        }).execute()
        print(f"✅ Bez filter funguje: {len(result.data)} výsledků")
    except Exception as e:
        print(f"❌ Bez filter: {e}")
    
    # Test 3: Pouze query_embedding
    print("\n🔧 Test 3: Pouze query_embedding...")
    try:
        result = supabase.rpc('match_gdanalyst', {
            'query_embedding': dummy_embedding
        }).execute()
        print(f"✅ Pouze embedding funguje: {len(result.data)} výsledků")
    except Exception as e:
        print(f"❌ Pouze embedding: {e}")
    
    # Test 4: Jiné pořadí parametrů
    print("\n🔧 Test 4: Jiné pořadí parametrů...")
    try:
        result = supabase.rpc('match_gdanalyst', {
            'query_embedding': dummy_embedding,
            'filter': {},
            'match_count': 3
        }).execute()
        print(f"✅ Jiné pořadí funguje: {len(result.data)} výsledků")
    except Exception as e:
        print(f"❌ Jiné pořadí: {e}")
    
    # Test 5: S konkrétním filtrem
    print("\n🔧 Test 5: S konkrétním filtrem...")
    try:
        result = supabase.rpc('match_gdanalyst', {
            'filter': {'source': 'test'},
            'match_count': 3,
            'query_embedding': dummy_embedding
        }).execute()
        print(f"✅ Konkrétní filter funguje: {len(result.data)} výsledků")
    except Exception as e:
        print(f"❌ Konkrétní filter: {e}")
    
    print("\n🎉 Test dokončen!")

if __name__ == "__main__":
    print("🧪 Test parametrů match_gdanalyst...")
    test_gdanalyst_params()
