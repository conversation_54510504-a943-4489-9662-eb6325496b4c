#!/usr/bin/env python3
"""
Test best practices nástro<PERSON><PERSON> bez crewai závislostí
"""

import os
import logging
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from openai import OpenAI
from supabase import create_client, Client
from dotenv import load_dotenv

# Načtení konfigurace
load_dotenv()

# Logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BestPracticesManager:
    """Správce best practices s Supabase a OpenAI integrací"""
    
    def __init__(self):
        self.supabase_client = self._initialize_supabase()
        self.openai_client = self._initialize_openai()
    
    def _initialize_supabase(self) -> Optional[Client]:
        """Inicializuje Supabase klienta"""
        try:
            url = os.getenv('SUPABASE_URL')
            key = os.getenv('SUPABASE_KEY')
            
            if not url or not key:
                logger.warning("⚠️ Supabase credentials nejsou nastavené")
                return None
            
            return create_client(url, key)
        except Exception as e:
            logger.error(f"❌ Chyba při inicializaci Supabase: {e}")
            return None
    
    def _initialize_openai(self) -> Optional[OpenAI]:
        """Inicializuje OpenAI klienta"""
        try:
            api_key = os.getenv('OPENAI_API_KEY')
            if not api_key:
                logger.warning("⚠️ OpenAI API key není nastavený")
                return None
            
            return OpenAI(api_key=api_key)
        except Exception as e:
            logger.error(f"❌ Chyba při inicializaci OpenAI: {e}")
            return None
    
    def _create_embedding(self, text: str) -> Optional[List[float]]:
        """Vytvoří embedding pro text pomocí OpenAI"""
        if not self.openai_client:
            logger.warning("⚠️ OpenAI není dostupné pro embedding")
            return None
        
        try:
            response = self.openai_client.embeddings.create(
                model="text-embedding-ada-002",
                input=text
            )
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"❌ Chyba při vytváření embeddingu: {e}")
            return None
    
    def save_best_practice(self, context_description: str, successful_strategy: str,
                          associated_agent_role: str, feedback_notes: List[str],
                          success_rating: float = 0.9) -> str:
        """Uloží best practice do Supabase"""
        
        if not self.supabase_client:
            return "❌ Supabase není dostupné - best practice nebyla uložena"
        
        try:
            # Vytvoření embeddingu pro kontext
            embedding = self._create_embedding(context_description)
            if not embedding:
                logger.warning("⚠️ Nepodařilo se vytvořit embedding, ukládám bez něj")
            
            # Příprava dat
            data = {
                'context_description': context_description,
                'successful_strategy': successful_strategy,
                'associated_agent_role': associated_agent_role,
                'feedback_notes': feedback_notes,
                'success_rating': success_rating,
                'embedding': embedding
            }
            
            # Uložení do Supabase
            result = self.supabase_client.table('best_practices').insert(data).execute()
            
            if result.data:
                practice_id = result.data[0]['id']
                logger.info(f"✅ Best practice uložena s ID: {practice_id}")
                return f"✅ Nová zkušenost úspěšně uložena s ID: {practice_id}"
            else:
                return "❌ Chyba při ukládání best practice"
                
        except Exception as e:
            logger.error(f"❌ Chyba při ukládání best practice: {e}")
            return f"❌ Chyba při ukládání: {str(e)}"
    
    def find_relevant_best_practices(self, current_situation_description: str,
                                   match_count: int = 2, match_threshold: float = 0.75) -> str:
        """Najde relevantní best practices pomocí sémantického vyhledávání"""
        
        if not self.supabase_client:
            return "❌ Supabase není dostupné - nelze vyhledávat best practices"
        
        try:
            # Vytvoření embeddingu pro dotaz
            query_embedding = self._create_embedding(current_situation_description)
            if not query_embedding:
                return "❌ Nepodařilo se vytvořit embedding pro vyhledávání"
            
            # Volání databázové funkce pro sémantické vyhledávání
            result = self.supabase_client.rpc(
                'match_best_practices',
                {
                    'query_embedding': query_embedding,
                    'match_threshold': match_threshold,
                    'match_count': match_count
                }
            ).execute()
            
            if not result.data:
                return "Nebyly nalezeny žádné relevantní zkušenosti z minulosti."
            
            # Formátování výsledků
            formatted_results = ["Nalezeny relevantní zkušenosti:\n"]
            
            for i, practice in enumerate(result.data, 1):
                similarity_percent = round(practice['similarity'] * 100, 1)
                formatted_results.append(f"ZKUŠENOST {i} (podobnost: {similarity_percent}%):")
                formatted_results.append(f"Situace: {practice['context_description']}")
                formatted_results.append(f"Doporučený postup: {practice['successful_strategy']}")
                formatted_results.append(f"Agent role: {practice['associated_agent_role']}")
                formatted_results.append("---")
            
            return "\n".join(formatted_results)
            
        except Exception as e:
            logger.error(f"❌ Chyba při vyhledávání best practices: {e}")
            return f"❌ Chyba při vyhledávání: {str(e)}"

def test_best_practices():
    """Test best practices systému"""
    print("🧪 Testování Best Practices systému...")
    
    manager = BestPracticesManager()
    
    # Test 1: Uložení best practice
    print("\n📝 Test 1: Ukládání best practice...")
    result = manager.save_best_practice(
        context_description="Klient měl nejasné požadavky na výzkum spokojenosti zaměstnanců",
        successful_strategy="Použil jsem strukturovaný dotazník pro upřesnění potřeb a cílů výzkumu",
        associated_agent_role="ClientOnboarder",
        feedback_notes=["Klient ocenil systematický přístup", "Výsledek byl přesně podle očekávání"],
        success_rating=0.9
    )
    print(f"Výsledek uložení: {result}")
    
    # Test 2: Vyhledávání best practices
    print("\n🔍 Test 2: Vyhledávání best practices...")
    result = manager.find_relevant_best_practices(
        current_situation_description="Klient neví přesně, co chce zkoumat v oblasti spokojenosti",
        match_count=2,
        match_threshold=0.7
    )
    print(f"Výsledek vyhledávání:\n{result}")
    
    # Test 3: Test OpenAI embedding
    print("\n🤖 Test 3: OpenAI embedding...")
    embedding = manager._create_embedding("Test text pro embedding")
    if embedding:
        print(f"✅ Embedding vytvořen, délka: {len(embedding)}")
    else:
        print("❌ Embedding se nepodařilo vytvořit")
    
    print("\n🎉 Testování dokončeno!")

if __name__ == "__main__":
    test_best_practices()
