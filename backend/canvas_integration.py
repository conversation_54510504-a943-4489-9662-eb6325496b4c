#!/usr/bin/env python3
"""
Canvas Integration - Integrace sdíleného plátna s agenty a orchestrátorem
"""

import logging
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime
from shared_project_canvas import (
    SharedProjectCanvas, AgentActivityTracker, 
    ActivityType, ActivityStatus, SectionStatus, ProjectPhase
)

logger = logging.getLogger(__name__)

# ============================================================================
# BASE AGENT S CANVAS INTEGRACÍ
# ============================================================================

class CanvasIntegratedAgent:
    """
    Základní třída pro agenty s integrací sdíleného plátna
    """
    
    def __init__(self, agent_id: str, canvas: SharedProjectCanvas = None, 
                 activity_tracker: AgentActivityTracker = None):
        self.agent_id = agent_id
        self.canvas = canvas
        self.activity_tracker = activity_tracker
        
    def set_canvas(self, canvas: SharedProjectCanvas, activity_tracker: AgentActivityTracker):
        """Nastaví sdílené plátno a activity tracker"""
        self.canvas = canvas
        self.activity_tracker = activity_tracker
        
    def log_activity(self, description: str, activity_type: ActivityType = ActivityType.THINKING,
                    status: ActivityStatus = ActivityStatus.STARTED, metadata: Dict = None) -> str:
        """Zaloguje aktivitu agenta"""
        if self.activity_tracker:
            return self.activity_tracker.log_activity(
                agent=self.agent_id,
                activity=description,
                status=status,
                activity_type=activity_type,
                metadata=metadata
            )
        return ""
    
    def update_canvas_section(self, section_id: str, content: str, 
                             reasoning: str = None, status: SectionStatus = SectionStatus.DRAFT) -> bool:
        """Standardní metoda pro aktualizaci plátna"""
        if not self.canvas:
            logger.warning(f"⚠️ {self.agent_id}: Canvas není nastaven")
            return False
            
        # Log start
        self.log_activity(
            f"Aktualizuji sekci '{section_id}'",
            ActivityType.UPDATING,
            ActivityStatus.STARTED
        )
        
        # Aktualizace
        success = self.canvas.update_by_agent(
            agent_id=self.agent_id,
            section=section_id,
            content=content,
            reasoning=reasoning,
            status=status
        )
        
        # Log completion
        if success:
            self.log_activity(
                f"Sekce '{section_id}' aktualizována",
                ActivityType.UPDATING,
                ActivityStatus.COMPLETED
            )
        else:
            self.log_activity(
                f"Chyba při aktualizaci sekce '{section_id}'",
                ActivityType.UPDATING,
                ActivityStatus.FAILED
            )
        
        return success
    
    def get_canvas_section(self, section_id: str) -> Optional[str]:
        """Získá obsah sekce z plátna"""
        if self.canvas:
            return self.canvas.get_section_content(section_id)
        return None
    
    def set_project_phase(self, phase: ProjectPhase):
        """Změní fázi projektu"""
        if self.canvas:
            self.canvas.set_phase(phase, self.agent_id)

# ============================================================================
# CANVAS ORCHESTRATOR
# ============================================================================

class CanvasOrchestrator:
    """
    Orchestrátor pro správu sdíleného plátna a koordinaci agentů
    """

    def __init__(self):
        self.canvases: Dict[str, SharedProjectCanvas] = {}
        self.activity_trackers: Dict[str, AgentActivityTracker] = {}
        self.websocket_notifier = None  # Bude nastaven z API serveru
        
    def get_or_create_canvas(self, session_id: str, project_name: str = "Nový Projekt") -> SharedProjectCanvas:
        """Získá nebo vytvoří plátno pro session"""
        if session_id not in self.canvases:
            self.canvases[session_id] = SharedProjectCanvas(
                project_name=project_name,
                session_id=session_id
            )
            self.activity_trackers[session_id] = AgentActivityTracker()
            logger.info(f"🎨 Vytvořeno nové plátno pro session: {session_id}")
        
        return self.canvases[session_id]
    
    def get_activity_tracker(self, session_id: str) -> Optional[AgentActivityTracker]:
        """Získá activity tracker pro session"""
        return self.activity_trackers.get(session_id)
    
    def integrate_agents_with_canvas(self, agents: List[Any], session_id: str):
        """Integruje agenty se sdíleným plátnem"""
        canvas = self.canvases.get(session_id)
        tracker = self.activity_trackers.get(session_id)

        if not canvas or not tracker:
            logger.error(f"❌ Canvas nebo tracker nenalezen pro session: {session_id}")
            return

        for agent in agents:
            if hasattr(agent, 'set_canvas'):
                agent.set_canvas(canvas, tracker)
            else:
                # Pro standardní CrewAI agenty použijeme __dict__ pro přidání atributů
                try:
                    agent.__dict__['canvas'] = canvas
                    agent.__dict__['activity_tracker'] = tracker
                    agent.__dict__['agent_id'] = getattr(agent, 'role', 'unknown_agent')
                    logger.info(f"✅ Agent {agent.role} integrován s canvas")
                except Exception as e:
                    logger.warning(f"⚠️ Nepodařilo se integrovat agent {getattr(agent, 'role', 'unknown')}: {e}")

        logger.info(f"✅ {len(agents)} agentů zpracováno pro canvas integraci")

    def set_websocket_notifier(self, notifier_func):
        """Nastaví funkci pro WebSocket notifikace"""
        self.websocket_notifier = notifier_func

    async def notify_canvas_update(self, session_id: str):
        """Notifikuje WebSocket klienty o aktualizaci canvas"""
        if self.websocket_notifier:
            try:
                await self.websocket_notifier(session_id)
            except Exception as e:
                logger.error(f"❌ Chyba při WebSocket notifikaci: {e}")
    
    def get_canvas_views(self, session_id: str) -> Dict[str, Any]:
        """Vrátí všechny pohledy na plátno"""
        canvas = self.canvases.get(session_id)
        tracker = self.activity_trackers.get(session_id)
        
        if not canvas:
            return {"error": "Canvas nenalezen"}
        
        return {
            "client_view": canvas.get_client_view(),
            "debug_view": canvas.get_debug_view(),
            "current_activities": tracker.get_current_activities() if tracker else [],
            "activity_history": tracker.get_activity_history(limit=10) if tracker else [],
            "metadata": {
                "project_name": canvas.metadata.project_name,
                "current_phase": canvas.metadata.current_phase.value,
                "last_updated": canvas.metadata.last_updated.isoformat(),
                "total_versions": canvas.metadata.total_versions
            }
        }
    
    def cleanup_old_sessions(self, max_age_hours: int = 24):
        """Vyčistí staré sessions"""
        current_time = datetime.now()
        sessions_to_remove = []
        
        for session_id, canvas in self.canvases.items():
            age = current_time - canvas.metadata.last_updated
            if age.total_seconds() > max_age_hours * 3600:
                sessions_to_remove.append(session_id)
        
        for session_id in sessions_to_remove:
            del self.canvases[session_id]
            if session_id in self.activity_trackers:
                del self.activity_trackers[session_id]
            logger.info(f"🧹 Vyčištěna stará session: {session_id}")

# ============================================================================
# HELPER FUNKCE PRO AGENTY
# ============================================================================

def create_canvas_aware_agent(agent_class, agent_id: str, *args, **kwargs):
    """Factory pro vytvoření agenta s canvas podporou"""
    agent = agent_class(*args, **kwargs)
    
    # Přidání canvas metod
    agent.agent_id = agent_id
    agent.canvas = None
    agent.activity_tracker = None
    
    def set_canvas(canvas: SharedProjectCanvas, activity_tracker: AgentActivityTracker):
        agent.canvas = canvas
        agent.activity_tracker = activity_tracker
    
    def log_activity(description: str, activity_type: ActivityType = ActivityType.THINKING,
                    status: ActivityStatus = ActivityStatus.STARTED):
        if agent.activity_tracker:
            return agent.activity_tracker.log_activity(
                agent=agent.agent_id,
                activity=description,
                status=status,
                activity_type=activity_type
            )
    
    def update_canvas_section(section_id: str, content: str, reasoning: str = None):
        if agent.canvas:
            return agent.canvas.update_by_agent(
                agent_id=agent.agent_id,
                section=section_id,
                content=content,
                reasoning=reasoning
            )
        return False
    
    # Přidání metod k agentovi
    agent.set_canvas = set_canvas
    agent.log_activity = log_activity
    agent.update_canvas_section = update_canvas_section
    
    return agent

# ============================================================================
# CANVAS MIDDLEWARE PRO CREWAI
# ============================================================================

class CanvasMiddleware:
    """
    Middleware pro automatickou integraci CrewAI s canvas systémem
    """
    
    def __init__(self, orchestrator: CanvasOrchestrator):
        self.orchestrator = orchestrator
    
    def wrap_crew_execution(self, crew, session_id: str, project_name: str = "Nový Projekt"):
        """Obalí crew execution s canvas integrací"""
        
        # Získání nebo vytvoření plátna
        canvas = self.orchestrator.get_or_create_canvas(session_id, project_name)
        tracker = self.orchestrator.get_activity_tracker(session_id)
        
        # Integrace agentů
        self.orchestrator.integrate_agents_with_canvas(crew.agents, session_id)
        
        # Původní kickoff metoda
        original_kickoff = crew.kickoff
        
        def canvas_aware_kickoff(*args, **kwargs):
            # Log začátku
            tracker.log_activity(
                agent="crew_orchestrator",
                activity="Spouštím crew execution",
                status=ActivityStatus.STARTED,
                activity_type=ActivityType.THINKING
            )
            
            # Spuštění původního kickoff
            result = original_kickoff(*args, **kwargs)
            
            # Log dokončení
            tracker.log_activity(
                agent="crew_orchestrator", 
                activity="Crew execution dokončen",
                status=ActivityStatus.COMPLETED,
                activity_type=ActivityType.THINKING
            )
            
            return result
        
        # Nahrazení kickoff metody
        crew.kickoff = canvas_aware_kickoff
        
        return crew

# ============================================================================
# GLOBÁLNÍ INSTANCE
# ============================================================================

# Globální orchestrátor pro sdílení mezi moduly
global_canvas_orchestrator = CanvasOrchestrator()

def get_global_canvas_orchestrator() -> CanvasOrchestrator:
    """Vrátí globální canvas orchestrátor"""
    return global_canvas_orchestrator

# ============================================================================
# TESTOVACÍ FUNKCE
# ============================================================================

if __name__ == "__main__":
    print("🧪 Testování Canvas Integration...")
    
    # Test orchestrátoru
    orchestrator = CanvasOrchestrator()
    
    # Vytvoření plátna
    canvas = orchestrator.get_or_create_canvas("test-session", "Test Integration")
    tracker = orchestrator.get_activity_tracker("test-session")
    
    # Test canvas-aware agenta
    class TestAgent(CanvasIntegratedAgent):
        def do_work(self):
            self.log_activity("Začínám práci", ActivityType.THINKING, ActivityStatus.STARTED)
            
            success = self.update_canvas_section(
                "project_overview",
                "Test integrace sdíleného plátna s agenty",
                "Testujeme základní funkcionalitu"
            )
            
            self.log_activity("Práce dokončena", ActivityType.THINKING, ActivityStatus.COMPLETED)
            return success
    
    # Test agenta
    agent = TestAgent("test_agent", canvas, tracker)
    result = agent.do_work()
    
    print(f"✅ Agent test: {result}")
    
    # Test views
    views = orchestrator.get_canvas_views("test-session")
    print(f"📊 Client view length: {len(views['client_view'])}")
    print(f"🔧 Debug view length: {len(views['debug_view'])}")
    print(f"🤖 Current activities: {len(views['current_activities'])}")
    
    print("🎉 Integration test dokončen!")
