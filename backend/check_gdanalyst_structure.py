#!/usr/bin/env python3
"""
Zkontroluje strukturu tabulky gdanalyst pro porovnání s best_practices
"""

import os
from supabase import create_client, Client
from dotenv import load_dotenv

load_dotenv()

def check_gdanalyst_structure():
    """Zkontroluje strukturu tabulky gdanalyst"""
    
    url = os.getenv('SUPABASE_URL')
    key = os.getenv('SUPABASE_KEY')
    
    supabase: Client = create_client(url, key)
    print("✅ Připojen k Supabase")
    
    # Test 1: Struktura tabulky gdanalyst
    print("\n📋 Test 1: Čtení z tabulky gdanalyst...")
    try:
        result = supabase.table('gdanalyst').select('*').limit(1).execute()
        if result.data:
            print(f"✅ Tabulka gdanalyst existuje, nalezen {len(result.data)} záznam")
            record = result.data[0]
            print("📊 Struktura záznamu:")
            for key, value in record.items():
                if key == 'embedding':
                    print(f"  - {key}: vector({len(value) if value else 0})")
                else:
                    print(f"  - {key}: {type(value).__name__} = {str(value)[:50]}...")
        else:
            print("⚠️ Tabulka gdanalyst je prázdná")
    except Exception as e:
        print(f"❌ Chyba při čtení gdanalyst: {e}")
    
    # Test 2: Test funkce match_gdanalyst
    print("\n🔧 Test 2: Funkce match_gdanalyst...")
    try:
        dummy_embedding = [0.1] * 1536
        result = supabase.rpc('match_gdanalyst', {
            'query_embedding': dummy_embedding,
            'match_threshold': 0.5,
            'match_count': 3
        }).execute()
        print(f"✅ Funkce match_gdanalyst funguje, nalezeno {len(result.data)} výsledků")
        if result.data:
            print("📊 Struktura výsledku:")
            for key, value in result.data[0].items():
                print(f"  - {key}: {type(value).__name__}")
    except Exception as e:
        print(f"❌ Funkce match_gdanalyst: {e}")
    
    # Test 3: Porovnání s best_practices
    print("\n📋 Test 3: Čtení z tabulky best_practices...")
    try:
        result = supabase.table('best_practices').select('*').limit(1).execute()
        if result.data:
            print(f"✅ Tabulka best_practices existuje, nalezen {len(result.data)} záznam")
            record = result.data[0]
            print("📊 Struktura záznamu:")
            for key, value in record.items():
                if key == 'embedding':
                    print(f"  - {key}: vector({len(value) if value else 0})")
                else:
                    print(f"  - {key}: {type(value).__name__} = {str(value)[:50]}...")
        else:
            print("⚠️ Tabulka best_practices je prázdná")
    except Exception as e:
        print(f"❌ Chyba při čtení best_practices: {e}")
    
    # Test 4: Porovnání sloupců
    print("\n🔍 Test 4: Porovnání sloupců...")
    
    # Zkusíme získat informace o sloupcích pomocí information_schema
    try:
        # Pro gdanalyst
        print("📊 Sloupce v gdanalyst:")
        result = supabase.rpc('exec', {
            'sql': "SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'gdanalyst' ORDER BY ordinal_position;"
        }).execute()
        print("  (Informace o sloupcích nejsou dostupné přes RPC)")
    except Exception as e:
        print(f"  Nelze získat informace o sloupcích: {e}")
    
    print("\n🎉 Kontrola dokončena!")

if __name__ == "__main__":
    print("🔍 Kontroluji strukturu tabulek...")
    check_gdanalyst_structure()
