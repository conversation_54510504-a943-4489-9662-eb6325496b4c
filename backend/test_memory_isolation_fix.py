#!/usr/bin/env python3
"""
Test opravy izolace paměti - ov<PERSON><PERSON><PERSON>, že vypnutí CrewAI globální paměti vyřešilo kontaminaci
"""

import os
import sys
from dotenv import load_dotenv

# <PERSON><PERSON><PERSON><PERSON><PERSON> backend adres<PERSON>ře do Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from session_handler import MatyldaSession

def test_memory_contamination_fix():
    """
    Test, že vypnutí CrewAI globální paměti vyřešilo problém kontaminace
    """
    print("🧪 === TEST OPRAVY KONTAMINACE PAMĚTI ===")
    print("Ově<PERSON>uje, že každá session dostává relevantní otázky pro své téma\n")
    
    # Test cases s velmi odlišnými tématy
    test_cases = [
        {
            "name": "Průzkum knihovny",
            "request": "Chci udělat průzkum spokojenosti s městskou knihovnou.",
            "expected_keywords": ["knihovn", "čten", "služb", "knihy"],
            "forbidden_keywords": ["doprav", "úklid", "park", "autobus", "silnic"]
        },
        {
            "name": "Průzkum sportu",
            "request": "Potřebuji průzkum spokojenosti se sportovními zařízeními.",
            "expected_keywords": ["sport", "zařízení", "tělocvičn", "hřišt"],
            "forbidden_keywords": ["knihovn", "doprav", "úklid", "autobus"]
        },
        {
            "name": "Průzkum zdravotnictví",
            "request": "Chci průzkum spokojenosti se zdravotními službami.",
            "expected_keywords": ["zdravot", "služb", "lékaři", "nemocnic"],
            "forbidden_keywords": ["sport", "knihovn", "doprav", "park"]
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- Test {i}: {test_case['name']} ---")
        
        try:
            # Vytvoření session
            session = MatyldaSession(test_case['request'])
            first_question = session.get_first_question()
            
            print(f"📝 Požadavek: {test_case['request']}")
            print(f"💬 Otázka: {first_question}")
            
            # Kontrola relevantnosti
            question_lower = first_question.lower()
            
            # Kontrola zakázaných klíčových slov (kontaminace)
            contamination_found = False
            for forbidden in test_case['forbidden_keywords']:
                if forbidden in question_lower:
                    print(f"❌ KONTAMINACE: Nalezeno zakázané slovo '{forbidden}'")
                    contamination_found = True
            
            # Kontrola relevantních klíčových slov
            relevance_found = False
            for expected in test_case['expected_keywords']:
                if expected in question_lower:
                    print(f"✅ RELEVANCE: Nalezeno relevantní slovo '{expected}'")
                    relevance_found = True
                    break
            
            # Hodnocení
            if contamination_found:
                print(f"❌ {test_case['name']}: KONTAMINACE DETEKOVÁNA")
                results.append(False)
            elif not relevance_found:
                print(f"⚠️ {test_case['name']}: Možná nedostatečná relevance")
                results.append(True)  # Není to chyba, jen méně ideální
            else:
                print(f"✅ {test_case['name']}: ČISTÁ A RELEVANTNÍ")
                results.append(True)
                
        except Exception as e:
            print(f"❌ Chyba v testu {test_case['name']}: {e}")
            results.append(False)
    
    # Shrnutí
    passed = sum(results)
    total = len(results)
    
    print(f"\n🔍 === SHRNUTÍ TESTŮ KONTAMINACE ===")
    print(f"Prošlo: {passed}/{total} testů")
    
    if passed == total:
        print("🎉 Všechny testy prošly! Kontaminace paměti je vyřešena.")
        return True
    else:
        print("⚠️ Některé testy selhaly. Kontaminace paměti stále existuje.")
        return False

def test_sequential_sessions():
    """
    Test postupného vytváření sessions - ověří, že se neovlivňují
    """
    print("\n\n🧪 === TEST POSTUPNÝCH SESSIONS ===")
    print("Testuje postupné vytváření sessions bez vzájemného ovlivnění\n")
    
    sessions = []
    topics = [
        "Průzkum spokojenosti s veřejným Wi-Fi",
        "Průzkum spokojenosti s městskými toaletami", 
        "Průzkum spokojenosti s pouličním osvětlením"
    ]
    
    # Vytvoření sessions postupně
    for i, topic in enumerate(topics, 1):
        print(f"📝 Vytváření Session {i}: {topic}")
        session = MatyldaSession(topic)
        question = session.get_first_question()
        sessions.append((topic, question))
        print(f"💬 Otázka {i}: {question[:80]}...")
    
    # Kontrola křížové kontaminace
    print(f"\n🔍 Kontrola křížové kontaminace:")
    
    contamination_detected = False
    
    # Wi-Fi session by neměla obsahovat informace o toaletách nebo osvětlení
    wifi_question = sessions[0][1].lower()
    if "toalet" in wifi_question or "osvětlen" in wifi_question:
        print("❌ Wi-Fi session obsahuje cizí témata!")
        contamination_detected = True
    else:
        print("✅ Wi-Fi session je čistá")
    
    # Toalety session by neměla obsahovat Wi-Fi nebo osvětlení
    toilet_question = sessions[1][1].lower()
    if "wi-fi" in toilet_question or "wifi" in toilet_question or "osvětlen" in toilet_question:
        print("❌ Toalety session obsahuje cizí témata!")
        contamination_detected = True
    else:
        print("✅ Toalety session je čistá")
    
    # Osvětlení session by neměla obsahovat Wi-Fi nebo toalety
    light_question = sessions[2][1].lower()
    if "wi-fi" in light_question or "wifi" in light_question or "toalet" in light_question:
        print("❌ Osvětlení session obsahuje cizí témata!")
        contamination_detected = True
    else:
        print("✅ Osvětlení session je čistá")
    
    if not contamination_detected:
        print("🎉 Žádná křížová kontaminace! Sessions jsou správně izolovány.")
        return True
    else:
        print("⚠️ Detekována křížová kontaminace mezi sessions.")
        return False

def test_context_preservation_within_session():
    """
    Test zachování kontextu v rámci jedné session
    """
    print("\n\n🧪 === TEST ZACHOVÁNÍ KONTEXTU V SESSION ===")
    print("Ověřuje, že session si stále pamatuje svůj vlastní kontext\n")
    
    session = MatyldaSession("Chci průzkum spokojenosti s městskými bazény.")
    
    # První krok
    first_question = session.get_first_question()
    print(f"💬 První otázka: {first_question}")
    
    # Druhý krok
    response = session.process_next_step("Zaměřujeme se na čistotu vody a bezpečnost.")
    print(f"🤖 Odpověď: {response[:100]}...")
    
    # Kontrola, zda si pamatuje kontext bazénů
    response_lower = response.lower()
    context_keywords = ["bazén", "vod", "čistot", "bezpečnost", "plavání"]
    
    context_found = False
    for keyword in context_keywords:
        if keyword in response_lower:
            print(f"✅ Kontext zachován: '{keyword}'")
            context_found = True
            break
    
    if context_found:
        print("✅ Session si správně pamatuje svůj kontext")
        return True
    else:
        print("⚠️ Session možná ztratila svůj kontext")
        return False

def main():
    """Hlavní testovací funkce"""
    load_dotenv()
    
    print("🚀 === TEST OPRAVY IZOLACE PAMĚTI ===")
    print("Ověřuje, že vypnutí CrewAI globální paměti vyřešilo kontaminaci\n")
    
    # Kontrola environment variables
    required_vars = ["OPENAI_API_KEY"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Chybí environment variables: {missing_vars}")
        return
    
    # Spuštění testů
    tests = [
        ("Oprava kontaminace", test_memory_contamination_fix),
        ("Postupné sessions", test_sequential_sessions),
        ("Zachování kontextu", test_context_preservation_within_session)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"Spouštím test: {test_name}")
        print('='*60)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test {test_name} selhal s chybou: {e}")
            import traceback
            traceback.print_exc()
            results.append((test_name, False))
    
    # Shrnutí výsledků
    print(f"\n{'='*60}")
    print("SHRNUTÍ TESTŮ OPRAVY")
    print('='*60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PROŠEL" if result else "❌ SELHAL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nVýsledek: {passed}/{len(results)} testů prošlo")
    
    if passed == len(results):
        print("🎉 Všechny testy prošly! Oprava izolace paměti je úspěšná.")
        print("✅ Sessions jsou nyní správně izolovány bez kontaminace.")
    else:
        print("⚠️ Některé testy selhaly. Problém s izolací paměti přetrvává.")

if __name__ == "__main__":
    main()
