#!/usr/bin/env python3
"""
Přímý test OpenAI API pro debugging
"""

import os
from openai import OpenAI
import json

# Načtení API klíče
api_key = os.getenv('OPENAI_API_KEY')
if not api_key:
    print("❌ OPENAI_API_KEY není nastavený!")
    exit(1)

client = OpenAI(api_key=api_key)

# Test prompt
prompt = """
Jsi autonomní projektový manažer a strategický konzultant s 15+ lety praxe.

UŽIVATELŮV POŽADAVEK: udělej mi návrh na anketu o spokojenosti občanů v Praze 21

INSTRUKCE:
1. Analyzuj požadavek holisticky
2. Vytvoř si mentální plán řešení
3. Dodej kompletní, profesionální výstup

FORMÁT ODPOVĚDI:
VŽDY odpověz POUZE validním JSON objektem s těmito kl<PERSON>:
- "chat_response": <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> odpověď pro uživatele (max 200 slov)
- "canvas_content": Kompletní strukturovaný Markdown dokument s detailním návrhem
- "thinking": Tvůj myšlenkový proces

PŘÍKLAD SPRÁVNÉHO FORMÁTU:
{
  "chat_response": "Připravil jsem pro vás kompletní návrh ankety...",
  "canvas_content": "# 📋 Návrh Ankety\\n\\n## Úvod\\n...",
  "thinking": "Uživatel chce anketu, takže..."
}

KRITICKÉ: Odpověz POUZE JSON objektem, žádný další text!
"""

try:
    print("🚀 Testuji OpenAI API...")
    
    response = client.chat.completions.create(
        model="gpt-4",
        messages=[
            {"role": "system", "content": prompt},
            {"role": "user", "content": "udělej mi návrh na anketu o spokojenosti občanů v Praze 21"}
        ],
        temperature=0.7,
        max_tokens=2000
    )
    
    ai_response = response.choices[0].message.content
    print("📝 Surová AI odpověď:")
    print(ai_response)
    print("\n" + "="*50 + "\n")
    
    # Pokus o parsování JSON
    try:
        parsed = json.loads(ai_response)
        print("✅ JSON je validní!")
        print(f"Chat response: {parsed.get('chat_response', 'CHYBÍ')[:100]}...")
        print(f"Canvas content: {parsed.get('canvas_content', 'CHYBÍ')[:100]}...")
        print(f"Thinking: {parsed.get('thinking', 'CHYBÍ')[:100]}...")
    except json.JSONDecodeError as e:
        print(f"❌ JSON není validní: {e}")
        
        # Pokus o vyčištění
        cleaned = ai_response.strip()
        if cleaned.startswith("```json"):
            cleaned = cleaned[7:]
        if cleaned.endswith("```"):
            cleaned = cleaned[:-3]
        cleaned = cleaned.strip()
        
        print("🧹 Pokus o vyčištění:")
        print(cleaned)
        
        try:
            parsed = json.loads(cleaned)
            print("✅ Vyčištěný JSON je validní!")
        except json.JSONDecodeError as e2:
            print(f"❌ Ani vyčištěný JSON není validní: {e2}")

except Exception as e:
    print(f"❌ Chyba při volání OpenAI: {e}")
