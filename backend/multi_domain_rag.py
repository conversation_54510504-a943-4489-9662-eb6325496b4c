#!/usr/bin/env python3
"""
Multi-Domain RAG systém pro Matylda
Implementuje podporu pro více izolovaných znalostních bází podle domény
"""

import os
import yaml
from typing import List, Optional, Dict, Any
from dotenv import load_dotenv
from crewai.tools import BaseTool
from pydantic import BaseModel, Field
from langchain_openai import OpenAIEmbeddings
from langchain_community.vectorstores import SupabaseVectorStore
from supabase import create_client, Client
import logging

# Načtení konfigurace
load_dotenv()

# Logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class KnowledgeBaseConfig(BaseModel):
    """Konfigurace pro jednu znalostní bázi"""
    id: str = Field(..., description="Unikátní identifikátor znalostní báze")
    table_name: str = Field(..., description="Název tabulky v Supabase")
    query_function: str = Field(..., description="Název funkce pro vyhledávání")
    description: str = Field(..., description="Popis znalostní báze")
    domain: Optional[str] = Field(None, description="Doména, ke které báze patří")

class MultiDomainRAGSystem:
    """
    Rozšířený RAG systém s podporou více izolovaných znalostních bází
    Každá doména může mít své vlastní znalostní báze
    """
    
    def __init__(self):
        """Inicializace multi-domain RAG systému"""
        self.supabase_url = os.getenv("SUPABASE_URL")
        self.supabase_key = os.getenv("SUPABASE_KEY")
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        
        self.supabase_client: Optional[Client] = None
        self.embeddings: Optional[OpenAIEmbeddings] = None
        self.knowledge_bases: Dict[str, KnowledgeBaseConfig] = {}
        self.vector_stores: Dict[str, SupabaseVectorStore] = {}
        
        self._initialize()
    
    def _initialize(self):
        """Inicializace všech komponent RAG systému"""
        try:
            # Validace konfigurace
            if not self.supabase_url or not self.supabase_key:
                logger.warning("Supabase konfigurace není kompletní - RAG systém nebude dostupný")
                return
            
            if not self.openai_api_key:
                logger.warning("OpenAI API klíč není nastaven - RAG systém nebude dostupný")
                return
            
            # Inicializace Supabase klienta
            self.supabase_client = create_client(self.supabase_url, self.supabase_key)
            
            # Inicializace embeddings
            self.embeddings = OpenAIEmbeddings(
                openai_api_key=self.openai_api_key,
                model=os.getenv("RAG_EMBEDDING_MODEL", "text-embedding-ada-002")
            )
            
            # Načtení konfigurace znalostních bází
            self._load_knowledge_base_configs()
            
            # Inicializace vector stores
            self._initialize_vector_stores()
            
            logger.info("✅ Multi-Domain RAG systém inicializován")
            
        except Exception as e:
            logger.error(f"❌ Chyba při inicializaci RAG systému: {e}")
    
    def _load_knowledge_base_configs(self):
        """Načte konfigurace znalostních bází z domain_knowledge_bases.yaml"""
        try:
            # Načtení z domain_knowledge_bases.yaml (priorita)
            kb_config_file = os.path.join(os.path.dirname(__file__), "config", "domain_knowledge_bases.yaml")

            if os.path.exists(kb_config_file):
                with open(kb_config_file, 'r', encoding='utf-8') as f:
                    kb_config = yaml.safe_load(f)

                # Načtení znalostních bází podle domén
                for domain_id, domain_data in kb_config.items():
                    if domain_id in ["search_config", "metadata_schema", "isolation_config", "monitoring", "cache_config", "metadata"]:
                        continue  # Přeskočit konfigurační sekce

                    if "knowledge_bases" in domain_data:
                        for kb_config_item in domain_data["knowledge_bases"]:
                            kb_config_item["domain"] = domain_id  # Přidání domény
                            kb = KnowledgeBaseConfig(**kb_config_item)
                            self.knowledge_bases[kb.id] = kb
                            logger.info(f"📚 Načtena znalostní báze: {kb.id} (doména: {domain_id})")

                logger.info(f"✅ Načteno {len(self.knowledge_bases)} znalostních bází z domain_knowledge_bases.yaml")

            # Fallback na domains.yaml
            elif self._load_from_domains_yaml():
                logger.info("✅ Načteno z domains.yaml jako fallback")

            # Fallback na agent konfigurace (pro kompatibilitu)
            elif not self.knowledge_bases:
                self._load_from_agent_configs()
                logger.info("✅ Načteno z agent konfigurací jako fallback")

            # Fallback na default konfiguraci
            if not self.knowledge_bases:
                default_kb = KnowledgeBaseConfig(
                    id="default_gdanalyst",
                    table_name=os.getenv("SUPABASE_TABLE_NAME", "gdanalyst"),
                    query_function=os.getenv("SUPABASE_QUERY_NAME", "match_gdanalyst"),
                    description="Default znalostní báze",
                    domain="default"
                )
                self.knowledge_bases[default_kb.id] = default_kb
                logger.info("📚 Použita default znalostní báze")

        except Exception as e:
            logger.error(f"❌ Chyba při načítání konfigurace znalostních bází: {e}")

    def _load_from_domains_yaml(self) -> bool:
        """Fallback načtení z domains.yaml"""
        try:
            domains_file = os.path.join(os.path.dirname(__file__), "config", "domains.yaml")

            if os.path.exists(domains_file):
                with open(domains_file, 'r', encoding='utf-8') as f:
                    domains_config = yaml.safe_load(f)

                # Načtení znalostních bází z domén
                for domain_id, domain_config in domains_config.get("domains", {}).items():
                    if "knowledge_bases" in domain_config:
                        for kb_config in domain_config["knowledge_bases"]:
                            kb_config["domain"] = domain_id  # Přidání domény
                            kb = KnowledgeBaseConfig(**kb_config)
                            self.knowledge_bases[kb.id] = kb
                            logger.info(f"📚 Načtena znalostní báze z domains.yaml: {kb.id} (doména: {domain_id})")

                # Načtení společných znalostních bází
                global_config = domains_config.get("global_config", {})
                shared_kbs = global_config.get("shared_knowledge_bases", [])
                for kb_config in shared_kbs:
                    kb_config["domain"] = "shared"
                    kb = KnowledgeBaseConfig(**kb_config)
                    self.knowledge_bases[kb.id] = kb
                    logger.info(f"📚 Načtena sdílená znalostní báze z domains.yaml: {kb.id}")

                return len(self.knowledge_bases) > 0

            return False

        except Exception as e:
            logger.error(f"❌ Chyba při načítání z domains.yaml: {e}")
            return False

    def _load_from_agent_configs(self):
        """Fallback načtení z agent konfigurací"""
        try:
            agents_dir = os.path.join(os.path.dirname(__file__), "agents")

            for yaml_file in os.listdir(agents_dir):
                if yaml_file.endswith(".yaml"):
                    file_path = os.path.join(agents_dir, yaml_file)
                    with open(file_path, 'r', encoding='utf-8') as f:
                        config = yaml.safe_load(f)

                    # Extrakce znalostních bází z agent konfigurací
                    for agent_name, agent_config in config.items():
                        if isinstance(agent_config, dict) and "knowledge_bases" in agent_config:
                            for kb_config in agent_config["knowledge_bases"]:
                                kb = KnowledgeBaseConfig(**kb_config)
                                self.knowledge_bases[kb.id] = kb
                                logger.info(f"📚 Načtena znalostní báze z agentů: {kb.id}")

        except Exception as e:
            logger.error(f"❌ Chyba při načítání z agent konfigurací: {e}")
    
    def _initialize_vector_stores(self):
        """Inicializuje vector stores pro všechny znalostní báze"""
        for kb_id, kb_config in self.knowledge_bases.items():
            try:
                vector_store = SupabaseVectorStore(
                    client=self.supabase_client,
                    embedding=self.embeddings,
                    table_name=kb_config.table_name,
                    query_name=kb_config.query_function
                )
                self.vector_stores[kb_id] = vector_store
                logger.info(f"✅ Vector store inicializován pro: {kb_id}")
                
            except Exception as e:
                logger.error(f"❌ Chyba při inicializaci vector store pro {kb_id}: {e}")
    
    def search_knowledge_base(self, query: str, knowledge_base_id: Optional[str] = None, 
                            domain: Optional[str] = None, k: int = 5) -> str:
        """
        Vyhledá informace v znalostní bázi
        
        Args:
            query: Vyhledávací dotaz
            knowledge_base_id: Specifická znalostní báze (volitelné)
            domain: Doména pro filtrování (volitelné)
            k: Počet výsledků
            
        Returns:
            Formátované výsledky vyhledávání
        """
        try:
            # Určení znalostní báze
            target_kb = None
            
            if knowledge_base_id and knowledge_base_id in self.vector_stores:
                target_kb = knowledge_base_id
            elif domain:
                # Najdi první znalostní bázi pro danou doménu
                for kb_id, kb_config in self.knowledge_bases.items():
                    if kb_config.domain == domain:
                        target_kb = kb_id
                        break
            else:
                # Použij první dostupnou znalostní bázi
                target_kb = next(iter(self.vector_stores.keys()), None)
            
            if not target_kb or target_kb not in self.vector_stores:
                return "Znalostní báze není dostupná."
            
            # Vyhledávání
            vector_store = self.vector_stores[target_kb]
            docs = vector_store.similarity_search(query, k=k)
            
            if not docs:
                return f"Nenalezeny žádné relevantní informace pro dotaz: {query}"
            
            # Formátování výsledků
            results = []
            for i, doc in enumerate(docs, 1):
                content = doc.page_content.strip()
                metadata = doc.metadata
                
                result = f"**Výsledek {i}:**\n{content}"
                if metadata:
                    result += f"\n*Metadata: {metadata}*"
                results.append(result)
            
            formatted_results = "\n\n".join(results)
            logger.info(f"🔍 Vyhledávání v {target_kb}: {len(docs)} výsledků")
            
            return formatted_results
            
        except Exception as e:
            logger.error(f"❌ Chyba při vyhledávání: {e}")
            return f"Chyba při vyhledávání: {str(e)}"
    
    def get_available_knowledge_bases(self, domain: Optional[str] = None) -> List[Dict[str, str]]:
        """Vrátí seznam dostupných znalostních bází"""
        result = []
        for kb_id, kb_config in self.knowledge_bases.items():
            if domain is None or kb_config.domain == domain:
                result.append({
                    "id": kb_id,
                    "description": kb_config.description,
                    "domain": kb_config.domain or "default",
                    "available": kb_id in self.vector_stores
                })
        return result
    
    def is_available(self) -> bool:
        """Kontroluje, zda je RAG systém dostupný"""
        return (self.supabase_client is not None and 
                self.embeddings is not None and 
                len(self.vector_stores) > 0)
    
    def get_status(self) -> Dict[str, Any]:
        """Vrátí status RAG systému"""
        return {
            "available": self.is_available(),
            "knowledge_bases_count": len(self.knowledge_bases),
            "vector_stores_count": len(self.vector_stores),
            "knowledge_bases": self.get_available_knowledge_bases()
        }

# Globální instance
multi_domain_rag = MultiDomainRAGSystem()

def get_multi_domain_rag() -> MultiDomainRAGSystem:
    """Vrátí globální instanci multi-domain RAG systému"""
    return multi_domain_rag

# CrewAI nástroj pro multi-domain vyhledávání
class MultiDomainKnowledgeSearchTool(BaseTool):
    name: str = "multi_domain_knowledge_search"
    description: str = "Vyhledává informace v doménově specifických znalostních bázích"
    
    def _run(self, query: str, domain: str = None, knowledge_base_id: str = None) -> str:
        """Spustí vyhledávání v multi-domain RAG systému"""
        return multi_domain_rag.search_knowledge_base(
            query=query,
            domain=domain,
            knowledge_base_id=knowledge_base_id
        )

# Vytvoření instance nástroje
multi_domain_knowledge_search = MultiDomainKnowledgeSearchTool()

if __name__ == "__main__":
    # Test multi-domain RAG systému
    print("🧪 Testování Multi-Domain RAG systému...")
    
    rag = MultiDomainRAGSystem()
    status = rag.get_status()
    print(f"📊 Status: {status}")
    
    if rag.is_available():
        # Test vyhledávání
        result = rag.search_knowledge_base("průzkum spokojenosti občanů")
        print(f"\n🔍 Výsledek vyhledávání:")
        print(result[:500] + "..." if len(result) > 500 else result)
    else:
        print("❌ RAG systém není dostupný")
