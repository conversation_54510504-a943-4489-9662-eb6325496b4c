#!/usr/bin/env python3
"""
Zkontroluje existující tabulky v Supabase a vytvoří best_practices tabulku
"""

import os
from supabase import create_client, Client
from dotenv import load_dotenv

load_dotenv()

def check_and_create_tables():
    """Zkontroluje a vytvoří potřebné tabulky"""
    
    url = os.getenv('SUPABASE_URL')
    key = os.getenv('SUPABASE_KEY')
    
    if not url or not key:
        print("❌ Chybí Supabase credentials")
        return False
    
    try:
        supabase: Client = create_client(url, key)
        print("✅ Připojen k Supabase")
        
        # Zkontroluj existující tabulky
        print("\n📋 Kontroluji existující tabulky...")
        
        # Pokus o čtení z různých tabulek
        tables_to_check = ['best_practices', 'gdanalyst', 'knowledge_base']
        
        for table_name in tables_to_check:
            try:
                result = supabase.table(table_name).select('*').limit(1).execute()
                print(f"✅ Tabulka '{table_name}' existuje ({len(result.data)} záznamů)")
            except Exception as e:
                print(f"❌ Tabulka '{table_name}' neexistuje: {e}")
        
        # Pokus o vytvoření best_practices tabulky pomocí INSERT
        print("\n🔧 Pokouším se vytvořit tabulku best_practices...")
        
        try:
            # Pokus o vložení testovacího záznamu (tabulka se vytvoří automaticky)
            test_data = {
                'context_description': 'Test situace',
                'successful_strategy': 'Test strategie', 
                'associated_agent_role': 'TestAgent',
                'success_rating': 0.9,
                'feedback_notes': ['Test poznámka']
            }
            
            result = supabase.table('best_practices').insert(test_data).execute()
            
            if result.data:
                print("✅ Tabulka best_practices vytvořena a test záznam vložen")
                # Smazání test záznamu
                test_id = result.data[0]['id']
                supabase.table('best_practices').delete().eq('id', test_id).execute()
                print("✅ Test záznam smazán")
                return True
            else:
                print("❌ Nepodařilo se vytvořit tabulku")
                return False
                
        except Exception as e:
            print(f"❌ Chyba při vytváření tabulky: {e}")
            
            # Pokus o ruční vytvoření pomocí SQL
            print("\n📝 Pokouším se vytvořit tabulku pomocí SQL...")
            
            create_sql = """
            CREATE TABLE IF NOT EXISTS best_practices (
                id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
                created_at timestamptz DEFAULT now(),
                context_description text NOT NULL,
                successful_strategy text NOT NULL,
                associated_agent_role text NOT NULL,
                success_rating float4 DEFAULT 0.95,
                feedback_notes text[] DEFAULT '{}'
            );
            """
            
            try:
                # Pokus o spuštění SQL přímo
                result = supabase.postgrest.rpc('exec', {'sql': create_sql}).execute()
                print("✅ Tabulka vytvořena pomocí SQL")
                return True
            except Exception as e2:
                print(f"❌ SQL vytvoření také selhalo: {e2}")
                
                print("\n📋 MANUÁLNÍ INSTRUKCE:")
                print("Prosím přejděte do Supabase Dashboard a vytvořte tabulku manuálně:")
                print("1. Otevřete https://supabase.com/dashboard")
                print("2. Vyberte váš projekt")
                print("3. Přejděte na 'SQL Editor'")
                print("4. Spusťte tento SQL:")
                print(create_sql)
                
                return False
    
    except Exception as e:
        print(f"❌ Obecná chyba: {e}")
        return False

if __name__ == "__main__":
    print("🔍 Kontroluji Supabase tabulky...")
    check_and_create_tables()
