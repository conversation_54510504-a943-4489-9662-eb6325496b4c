#!/usr/bin/env python3
"""
Test izolace sessions - o<PERSON><PERSON><PERSON><PERSON>, zda nedochází k úniku informací mezi sessions
Tento test je kritický pro ověření, že každá session je skutečně izolovaná
"""

import os
import sys
import json
import time
from dotenv import load_dotenv

# Přidání backend adresáře do Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from session_handler import MatyldaSession

def test_session_isolation():
    """
    Test izolace sessions - vytvoří dvě různé sessions s různými tématy
    a ověří, že si nenavzájem "kontaminují" paměť
    """
    print("🧪 === TEST IZOLACE SESSIONS ===")
    print("Testuje, zda nedochází k úniku informací mezi různými sessions\n")
    
    # Session 1: Průzkum parku
    print("📝 Vytváření Session 1: Průzkum parku")
    session1 = MatyldaSession("Chci udělat průzkum spokojenosti s městským parkem.")
    
    first_question1 = session1.get_first_question()
    print(f"💬 Session 1 - První otázka: {first_question1}")
    
    response1_1 = session1.process_next_step("Park má sloužit především rodinám s malými dětmi.")
    print(f"🤖 Session 1 - Odpověď 1: {response1_1[:100]}...")
    
    # Session 2: Průzkum dopravy (úplně jiné téma)
    print("\n📝 Vytváření Session 2: Průzkum dopravy")
    session2 = MatyldaSession("Potřebuji průzkum spokojenosti s městskou hromadnou dopravou.")
    
    first_question2 = session2.get_first_question()
    print(f"💬 Session 2 - První otázka: {first_question2}")
    
    response2_1 = session2.process_next_step("Zaměřujeme se na autobusy a jejich spolehlivost.")
    print(f"🤖 Session 2 - Odpověď 1: {response2_1[:100]}...")
    
    # Pokračování v Session 1 - test, zda si pamatuje své téma
    print("\n🔄 Pokračování v Session 1")
    response1_2 = session1.process_next_step("Rozpočet máme 500 tisíc korun.")
    print(f"🤖 Session 1 - Odpověď 2: {response1_2[:100]}...")
    
    # Pokračování v Session 2 - test, zda si pamatuje své téma
    print("\n🔄 Pokračování v Session 2")
    response2_2 = session2.process_next_step("Máme problémy s pozdními spoji.")
    print(f"🤖 Session 2 - Odpověď 2: {response2_2[:100]}...")
    
    # Analýza kontaminace
    print("\n🔍 === ANALÝZA KONTAMINACE ===")
    
    # Session 1 by neměla obsahovat informace o dopravě
    contamination_session1 = False
    transport_keywords = ["autobus", "doprav", "spoj", "MHD", "autobus"]
    for keyword in transport_keywords:
        if keyword.lower() in response1_2.lower():
            print(f"⚠️ Session 1 obsahuje dopravní klíčové slovo: '{keyword}'")
            contamination_session1 = True
    
    # Session 2 by neměla obsahovat informace o parku
    contamination_session2 = False
    park_keywords = ["park", "děti", "rodin", "hřiště", "zeleň"]
    for keyword in park_keywords:
        if keyword.lower() in response2_2.lower():
            print(f"⚠️ Session 2 obsahuje parkové klíčové slovo: '{keyword}'")
            contamination_session2 = True
    
    # Výsledek testu
    if not contamination_session1 and not contamination_session2:
        print("✅ Sessions jsou správně izolovány - žádná kontaminace!")
        return True
    else:
        print("❌ Detekována kontaminace mezi sessions!")
        return False

def test_memory_persistence_within_session():
    """
    Test persistence paměti v rámci jedné session
    """
    print("\n\n🧪 === TEST PERSISTENCE PAMĚTI V SESSION ===")
    print("Testuje, zda si session pamatuje svou vlastní historii\n")
    
    session = MatyldaSession("Chci průzkum o kvalitě veřejného osvětlení.")
    
    # První krok
    first_question = session.get_first_question()
    print(f"💬 První otázka: {first_question}")
    
    response1 = session.process_next_step("Zaměřujeme se na bezpečnost v noci.")
    print(f"🤖 Odpověď 1: {response1[:100]}...")
    
    # Druhý krok - měl by odkazovat na bezpečnost
    response2 = session.process_next_step("Máme rozpočet 2 miliony korun.")
    print(f"🤖 Odpověď 2: {response2[:100]}...")
    
    # Kontrola, zda si pamatuje předchozí kontext
    memory_keywords = ["bezpečnost", "noc", "osvětlen"]
    memory_found = False
    for keyword in memory_keywords:
        if keyword.lower() in response2.lower():
            print(f"✅ Session si pamatuje kontext: '{keyword}'")
            memory_found = True
            break
    
    if not memory_found:
        print("⚠️ Session si možná nepamatuje předchozí kontext")
        return False
    
    return True

def test_server_restart_simulation():
    """
    Test simulace restartu serveru - ověří, že sessions se skutečně vynulují
    """
    print("\n\n🧪 === TEST SIMULACE RESTARTU SERVERU ===")
    print("Simuluje restart serveru a ověří vynulování sessions\n")
    
    # Simulace active_sessions slovníku (jako v api_server.py)
    active_sessions = {}
    
    # Vytvoření session
    session_id = "test-session-123"
    session = MatyldaSession("Test požadavek před restartem")
    active_sessions[session_id] = session
    
    print(f"📊 Před 'restartem': {len(active_sessions)} aktivních sessions")
    
    # Simulace restartu - vynulování slovníku
    active_sessions.clear()  # Toto se stane při restartu serveru
    
    print(f"📊 Po 'restartu': {len(active_sessions)} aktivních sessions")
    
    # Pokus o přístup k session po restartu
    if session_id not in active_sessions:
        print("✅ Session byla správně vynulována po restartu")
        return True
    else:
        print("❌ Session přežila restart - to je problém!")
        return False

def test_crewai_memory_isolation():
    """
    Test izolace CrewAI paměti mezi různými instancemi
    """
    print("\n\n🧪 === TEST IZOLACE CREWAI PAMĚTI ===")
    print("Testuje, zda CrewAI memory=True nesdílí paměť mezi instancemi\n")
    
    # Vytvoření dvou sessions s velmi odlišnými tématy
    session_a = MatyldaSession("Průzkum spokojenosti se zimní údržbou silnic")
    session_b = MatyldaSession("Průzkum spokojenosti s letními kulturními akcemi")
    
    # Získání prvních otázek
    question_a = session_a.get_first_question()
    question_b = session_b.get_first_question()
    
    print(f"💬 Session A otázka: {question_a[:80]}...")
    print(f"💬 Session B otázka: {question_b[:80]}...")
    
    # Kontrola, zda se otázky neovlivňují
    winter_keywords = ["zim", "sníh", "posyp", "silnic", "údržb"]
    summer_keywords = ["lét", "kultur", "akc", "festival", "koncert"]
    
    # Session A by neměla obsahovat letní témata
    contamination_a = any(keyword in question_a.lower() for keyword in summer_keywords)
    # Session B by neměla obsahovat zimní témata  
    contamination_b = any(keyword in question_b.lower() for keyword in winter_keywords)
    
    if contamination_a:
        print("❌ Session A obsahuje letní témata!")
        return False
    
    if contamination_b:
        print("❌ Session B obsahuje zimní témata!")
        return False
    
    print("✅ CrewAI paměť je správně izolována mezi instancemi")
    return True

def main():
    """Hlavní testovací funkce"""
    load_dotenv()
    
    print("🚀 === TESTOVÁNÍ IZOLACE SESSIONS ===")
    print("Kritický test pro ověření, že sessions nesdílejí informace\n")
    
    # Kontrola environment variables
    required_vars = ["OPENAI_API_KEY"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Chybí environment variables: {missing_vars}")
        return
    
    # Spuštění testů
    tests = [
        ("Izolace Sessions", test_session_isolation),
        ("Persistence paměti", test_memory_persistence_within_session),
        ("Simulace restartu", test_server_restart_simulation),
        ("Izolace CrewAI paměti", test_crewai_memory_isolation)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"Spouštím test: {test_name}")
        print('='*60)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Test {test_name} selhal s chybou: {e}")
            import traceback
            traceback.print_exc()
            results.append((test_name, False))
    
    # Shrnutí výsledků
    print(f"\n{'='*60}")
    print("SHRNUTÍ TESTŮ IZOLACE")
    print('='*60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PROŠEL" if result else "❌ SELHAL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nVýsledek: {passed}/{len(results)} testů prošlo")
    
    if passed == len(results):
        print("🎉 Všechny testy izolace prošly! Sessions jsou správně izolovány.")
    else:
        print("⚠️ Některé testy selhaly. KRITICKÝ PROBLÉM s izolací sessions!")
        print("🔧 Doporučení: Zkontrolujte CrewAI memory management a session handling")

if __name__ == "__main__":
    main()
