#!/usr/bin/env python3
"""
Session Handler pro Matylda - Architektura "Týmu Specialistů"
Používá SpecialistOrchestrator pro řízení tří specializovaných agentů
"""

import os
import json
import logging
import uuid
from typing import Dict, Any, Optional, List
from dotenv import load_dotenv
from specialist_orchestrator import get_specialist_orchestrator, BriefState

# Logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MatyldaSession:
    """
    Stavová třída pro Matylda s architekturou "Týmu Specialistů"
    Používá SpecialistOrchestrator pro řízení tří specializovaných agentů
    """

    def __init__(self, initial_request: str):
        """
        Inicializace session s počátečním požadavkem.
        Používá SpecialistOrchestrator pro týmovou spolupráci agentů.

        Args:
            initial_request: Počáteční požadavek klienta
        """
        load_dotenv()

        # Načtení konfigurací
        self.max_iterations = int(os.getenv("MAX_SESSION_ITERATIONS", "20"))
        self.verbose = os.getenv("CONVERSATIONAL_VERBOSE", "True").lower() == "true"

        # Inicializace specialist orchestrátoru
        self.orchestrator = get_specialist_orchestrator()

        # Session data
        self.initial_request = initial_request
        self.chat_history: List[Dict[str, str]] = []
        self.current_iteration = 0
        self.is_complete = False
        self.final_analysis: Optional[Dict[str, Any]] = None

        # Stav briefu
        self.brief_state = BriefState()

        # Unikátní identifikátor
        self.session_id = str(uuid.uuid4())
        logger.info(f"🔑 Session ID: {self.session_id}")

        # Generování první otázky pomocí orchestrátoru
        self.first_question = self._generate_first_question()

        logger.info(f"✅ MatyldaSession inicializována s SpecialistOrchestrator")
        logger.info(f"📝 Počáteční požadavek: {initial_request}")

    def _generate_first_question(self) -> str:
        """Generuje první otázku pomocí SpecialistOrchestrator"""
        try:
            # Zpracování pomocí SpecialistOrchestrator
            result = self.orchestrator.process_message(
                user_message=self.initial_request,
                chat_history=[],  # Prázdná historie pro první zprávu
                brief_state=self.brief_state,
                session_id=self.session_id
            )

            # Aktualizace stavu
            self.brief_state = result.brief_state
            self.is_complete = result.is_complete

            # Přidání do historie
            self.chat_history.append({
                "type": "user",
                "content": self.initial_request,
                "iteration": 0
            })
            self.chat_history.append({
                "type": "agent",
                "content": result.chat_response,
                "iteration": 0,
                "canvas_content": result.canvas_content,
                "completion_percentage": result.completion_percentage
            })

            logger.info("✅ První otázka vygenerována pomocí SpecialistOrchestrator")
            return result.chat_response

        except Exception as e:
            logger.error(f"❌ Chyba při generování první otázky: {e}")
            return "Můžete mi prosím říct více o vašem projektu a jeho hlavních cílech?"

    def get_first_question(self) -> str:
        """Vrátí první otázku pro klienta"""
        return self.first_question

    def process_next_step(self, user_input: str) -> Dict[str, Any]:
        """
        Hlavní metoda pro zpracování další zprávy od uživatele.
        Používá SpecialistOrchestrator pro týmovou spolupráci.

        Args:
            user_input: Zpráva od uživatele

        Returns:
            Slovník s odpovědí agenta a canvas obsahem
        """
        if self.is_complete:
            return {
                "chat_response": "Konverzace je již dokončena.",
                "canvas_content": self._get_final_canvas(),
                "is_complete": True
            }

        if self.current_iteration >= self.max_iterations:
            logger.warning("⚠️ Dosažen maximální počet iterací, ukončujem konverzaci")
            return self._force_finalization()

        try:
            # Zpracování pomocí SpecialistOrchestrator
            result = self.orchestrator.process_message(
                user_message=user_input,
                chat_history=self.chat_history,
                brief_state=self.brief_state,
                session_id=self.session_id
            )

            # Aktualizace stavu
            self.brief_state = result.brief_state
            self.is_complete = result.is_complete

            # Kontrola dokončení
            if result.is_complete:
                self.final_analysis = self.brief_state.model_dump()
                logger.info("✅ Konverzace dokončena")

            # Přidání zpráv do historie
            self.chat_history.append({
                "type": "user",
                "content": user_input,
                "iteration": self.current_iteration
            })
            self.chat_history.append({
                "type": "agent",
                "content": result.chat_response,
                "iteration": self.current_iteration,
                "canvas_content": result.canvas_content,
                "completion_percentage": result.completion_percentage
            })

            self.current_iteration += 1

            logger.info(f"✅ Krok {self.current_iteration} dokončen ({result.completion_percentage}%)")

            return {
                "chat_response": result.chat_response,
                "canvas_content": result.canvas_content,
                "is_complete": result.is_complete,
                "completion_percentage": result.completion_percentage
            }

        except Exception as e:
            logger.error(f"❌ Chyba při zpracování kroku: {e}")
            return {
                "chat_response": f"Omlouvám se, došlo k chybě: {str(e)}",
                "canvas_content": self._get_fallback_canvas(),
                "is_complete": False,
                "completion_percentage": 0
            }

    def _force_finalization(self) -> Dict[str, Any]:
        """Vynucené ukončení při dosažení limitu iterací"""
        self.is_complete = True

        # Vytvoření finální analýzy ze současného stavu
        self.final_analysis = self.brief_state.model_dump()

        return {
            "chat_response": "Dosáhli jsme maximálního počtu otázek. Na základě získaných informací připravíme návrh projektu. Pokud potřebujete pokračovat, začněte prosím novou konverzaci.",
            "canvas_content": self._get_final_canvas(),
            "is_complete": True,
            "completion_percentage": 100
        }

    def _get_final_canvas(self) -> str:
        """Vrátí finální canvas obsah"""
        try:
            return self.orchestrator._generate_fallback_canvas(self.brief_state)
        except:
            return self._get_fallback_canvas()

    def _get_fallback_canvas(self) -> str:
        """Vrátí fallback canvas obsah"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%d.%m.%Y %H:%M")

        return f"""# 📋 Projektové Zadání

## 🎯 Cíl Projektu
{self.brief_state.projekt_cil or "Zatím neurčeno"}

## 🔑 Klíčové Rozhodnutí
{self.brief_state.klicove_rozhodnuti or "Zatím neurčeno"}

## 👥 Cílová Skupina
{self.brief_state.cilova_skupina or "Zatím neurčeno"}

## 📊 Požadované Analýzy
{self.brief_state.pozadovane_analyzy or "Zatím neurčeno"}

---
*Aktualizováno: {timestamp}*"""

    def get_first_question(self) -> str:
        """Vrátí první otázku pro klienta"""
        return self.first_question

    def get_session_summary(self) -> Dict[str, Any]:
        """Vrátí shrnutí celé session"""
        return {
            'initial_request': self.initial_request,
            'chat_history': self.chat_history,
            'current_iteration': self.current_iteration,
            'is_complete': self.is_complete,
            'final_analysis': self.final_analysis,
            'brief_state': self.brief_state.model_dump(),
            'total_messages': len([msg for msg in self.chat_history if msg['type'] == 'user']),
            'completion_percentage': self._calculate_completion_percentage()
        }

    def _calculate_completion_percentage(self) -> int:
        """Vypočítá procento dokončení briefu"""
        required_fields = ['projekt_cil', 'klicove_rozhodnuti', 'cilova_skupina']
        filled_fields = 0

        for field in required_fields:
            value = getattr(self.brief_state, field, None)
            if value and value.strip():
                filled_fields += 1

        return int((filled_fields / len(required_fields)) * 100)

    def is_session_complete(self) -> bool:
        """Vrátí True, pokud je session dokončena"""
        return self.is_complete

    def get_final_analysis(self) -> Optional[Dict[str, Any]]:
        """Vrátí finální analýzu, pokud je dostupná"""
        return self.final_analysis

if __name__ == "__main__":
    # Test session handleru
    print("🧪 Testování MatyldaSession s novým ConversationalAgent...")

    # Vytvoření session
    session = MatyldaSession("Potřebuji udělat průzkum spokojenosti občanů.")
    print(f"✅ Session vytvořena")
    print(f"📝 První otázka: {session.get_first_question()}")

    # Test zpracování odpovědi
    response = session.process_next_step("Chceme zjistit, jak jsou občané spokojeni s místními službami.")
    print(f"🤖 Odpověď agenta: {response}")

    # Status session
    summary = session.get_session_summary()
    print(f"📊 Stav session: Iterace {summary['current_iteration']}, Dokončeno: {summary['is_complete']}")
    print(f"🗂️ Stav briefu: {summary['brief_state']}")
    print(f"📈 Dokončení: {summary['completion_percentage']}%")
