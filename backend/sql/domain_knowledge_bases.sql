-- SQL skripty pro vytvoření izolovaných znalostních bází podle domén
-- Matylda v2.0 - <PERSON><PERSON><PERSON>lovatelná architektura

-- =====================================================
-- DOMÉNA: Onboarding Průzkumů
-- =====================================================

-- Tabulka pro metodologie průzkumů
CREATE TABLE IF NOT EXISTS kb_onboarding_pruzkumy (
    id BIGSERIAL PRIMARY KEY,
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    embedding VECTOR(1536),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pro rychlé vyhledávání
CREATE INDEX IF NOT EXISTS idx_kb_onboarding_pruzkumy_embedding 
ON kb_onboarding_pruzkumy USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);

-- Funkce pro vyhledávání v onboarding průzkumech
CREATE OR REPLACE FUNCTION match_onboarding_pruzkumy(
  query_embedding VECTOR(1536),
  match_threshold FLOAT DEFAULT 0.78,
  match_count INT DEFAULT 5
)
RETURNS TABLE (
  id BIGINT,
  content TEXT,
  metadata JSONB,
  similarity FLOAT
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    kb_onboarding_pruzkumy.id,
    kb_onboarding_pruzkumy.content,
    kb_onboarding_pruzkumy.metadata,
    1 - (kb_onboarding_pruzkumy.embedding <=> query_embedding) AS similarity
  FROM kb_onboarding_pruzkumy
  WHERE 1 - (kb_onboarding_pruzkumy.embedding <=> query_embedding) > match_threshold
  ORDER BY kb_onboarding_pruzkumy.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;

-- Tabulka pro best practices onboarding
CREATE TABLE IF NOT EXISTS kb_best_practices_onboarding (
    id BIGSERIAL PRIMARY KEY,
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    embedding VECTOR(1536),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pro best practices onboarding
CREATE INDEX IF NOT EXISTS idx_kb_best_practices_onboarding_embedding 
ON kb_best_practices_onboarding USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);

-- Funkce pro vyhledávání v best practices onboarding
CREATE OR REPLACE FUNCTION match_best_practices_onboarding(
  query_embedding VECTOR(1536),
  match_threshold FLOAT DEFAULT 0.78,
  match_count INT DEFAULT 5
)
RETURNS TABLE (
  id BIGINT,
  content TEXT,
  metadata JSONB,
  similarity FLOAT
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    kb_best_practices_onboarding.id,
    kb_best_practices_onboarding.content,
    kb_best_practices_onboarding.metadata,
    1 - (kb_best_practices_onboarding.embedding <=> query_embedding) AS similarity
  FROM kb_best_practices_onboarding
  WHERE 1 - (kb_best_practices_onboarding.embedding <=> query_embedding) > match_threshold
  ORDER BY kb_best_practices_onboarding.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;

-- =====================================================
-- DOMÉNA: Sales Qualification
-- =====================================================

-- Tabulka pro sales materiály
CREATE TABLE IF NOT EXISTS kb_sales_materials (
    id BIGSERIAL PRIMARY KEY,
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    embedding VECTOR(1536),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pro sales materiály
CREATE INDEX IF NOT EXISTS idx_kb_sales_materials_embedding 
ON kb_sales_materials USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);

-- Funkce pro vyhledávání v sales materiálech
CREATE OR REPLACE FUNCTION match_sales_materials(
  query_embedding VECTOR(1536),
  match_threshold FLOAT DEFAULT 0.78,
  match_count INT DEFAULT 5
)
RETURNS TABLE (
  id BIGINT,
  content TEXT,
  metadata JSONB,
  similarity FLOAT
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    kb_sales_materials.id,
    kb_sales_materials.content,
    kb_sales_materials.metadata,
    1 - (kb_sales_materials.embedding <=> query_embedding) AS similarity
  FROM kb_sales_materials
  WHERE 1 - (kb_sales_materials.embedding <=> query_embedding) > match_threshold
  ORDER BY kb_sales_materials.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;

-- Tabulka pro analýzu konkurence
CREATE TABLE IF NOT EXISTS kb_competitors (
    id BIGSERIAL PRIMARY KEY,
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    embedding VECTOR(1536),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pro analýzu konkurence
CREATE INDEX IF NOT EXISTS idx_kb_competitors_embedding 
ON kb_competitors USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);

-- Funkce pro vyhledávání v analýze konkurence
CREATE OR REPLACE FUNCTION match_competitors(
  query_embedding VECTOR(1536),
  match_threshold FLOAT DEFAULT 0.78,
  match_count INT DEFAULT 5
)
RETURNS TABLE (
  id BIGINT,
  content TEXT,
  metadata JSONB,
  similarity FLOAT
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    kb_competitors.id,
    kb_competitors.content,
    kb_competitors.metadata,
    1 - (kb_competitors.embedding <=> query_embedding) AS similarity
  FROM kb_competitors
  WHERE 1 - (kb_competitors.embedding <=> query_embedding) > match_threshold
  ORDER BY kb_competitors.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;

-- =====================================================
-- DOMÉNA: Participativní Rozpočet
-- =====================================================

-- Tabulka pro metodiky participace
CREATE TABLE IF NOT EXISTS kb_participace (
    id BIGSERIAL PRIMARY KEY,
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    embedding VECTOR(1536),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pro metodiky participace
CREATE INDEX IF NOT EXISTS idx_kb_participace_embedding 
ON kb_participace USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);

-- Funkce pro vyhledávání v metodikách participace
CREATE OR REPLACE FUNCTION match_participace(
  query_embedding VECTOR(1536),
  match_threshold FLOAT DEFAULT 0.78,
  match_count INT DEFAULT 5
)
RETURNS TABLE (
  id BIGINT,
  content TEXT,
  metadata JSONB,
  similarity FLOAT
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    kb_participace.id,
    kb_participace.content,
    kb_participace.metadata,
    1 - (kb_participace.embedding <=> query_embedding) AS similarity
  FROM kb_participace
  WHERE 1 - (kb_participace.embedding <=> query_embedding) > match_threshold
  ORDER BY kb_participace.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;

-- Tabulka pro pravidla rozpočtování
CREATE TABLE IF NOT EXISTS kb_rozpocet (
    id BIGSERIAL PRIMARY KEY,
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    embedding VECTOR(1536),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pro pravidla rozpočtování
CREATE INDEX IF NOT EXISTS idx_kb_rozpocet_embedding 
ON kb_rozpocet USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);

-- Funkce pro vyhledávání v pravidlech rozpočtování
CREATE OR REPLACE FUNCTION match_rozpocet(
  query_embedding VECTOR(1536),
  match_threshold FLOAT DEFAULT 0.78,
  match_count INT DEFAULT 5
)
RETURNS TABLE (
  id BIGINT,
  content TEXT,
  metadata JSONB,
  similarity FLOAT
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    kb_rozpocet.id,
    kb_rozpocet.content,
    kb_rozpocet.metadata,
    1 - (kb_rozpocet.embedding <=> query_embedding) AS similarity
  FROM kb_rozpocet
  WHERE 1 - (kb_rozpocet.embedding <=> query_embedding) > match_threshold
  ORDER BY kb_rozpocet.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;

-- =====================================================
-- DOMÉNA: Customer Support
-- =====================================================

-- Tabulka pro znalostní bázi podpory
CREATE TABLE IF NOT EXISTS kb_support (
    id BIGSERIAL PRIMARY KEY,
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    embedding VECTOR(1536),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pro znalostní bázi podpory
CREATE INDEX IF NOT EXISTS idx_kb_support_embedding 
ON kb_support USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);

-- Funkce pro vyhledávání ve znalostní bázi podpory
CREATE OR REPLACE FUNCTION match_support(
  query_embedding VECTOR(1536),
  match_threshold FLOAT DEFAULT 0.78,
  match_count INT DEFAULT 5
)
RETURNS TABLE (
  id BIGINT,
  content TEXT,
  metadata JSONB,
  similarity FLOAT
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    kb_support.id,
    kb_support.content,
    kb_support.metadata,
    1 - (kb_support.embedding <=> query_embedding) AS similarity
  FROM kb_support
  WHERE 1 - (kb_support.embedding <=> query_embedding) > match_threshold
  ORDER BY kb_support.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;

-- =====================================================
-- SPOLEČNÉ ZNALOSTNÍ BÁZE
-- =====================================================

-- Tabulka pro obecné best practices
CREATE TABLE IF NOT EXISTS kb_general (
    id BIGSERIAL PRIMARY KEY,
    content TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    embedding VECTOR(1536),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pro obecné best practices
CREATE INDEX IF NOT EXISTS idx_kb_general_embedding 
ON kb_general USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);

-- Funkce pro vyhledávání v obecných best practices
CREATE OR REPLACE FUNCTION match_general(
  query_embedding VECTOR(1536),
  match_threshold FLOAT DEFAULT 0.78,
  match_count INT DEFAULT 5
)
RETURNS TABLE (
  id BIGINT,
  content TEXT,
  metadata JSONB,
  similarity FLOAT
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    kb_general.id,
    kb_general.content,
    kb_general.metadata,
    1 - (kb_general.embedding <=> query_embedding) AS similarity
  FROM kb_general
  WHERE 1 - (kb_general.embedding <=> query_embedding) > match_threshold
  ORDER BY kb_general.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;

-- =====================================================
-- TRIGGERY PRO AUTOMATICKOU AKTUALIZACI
-- =====================================================

-- Trigger funkce pro updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Aplikace triggerů na všechny tabulky
CREATE TRIGGER update_kb_onboarding_pruzkumy_updated_at BEFORE UPDATE ON kb_onboarding_pruzkumy FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_kb_best_practices_onboarding_updated_at BEFORE UPDATE ON kb_best_practices_onboarding FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_kb_sales_materials_updated_at BEFORE UPDATE ON kb_sales_materials FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_kb_competitors_updated_at BEFORE UPDATE ON kb_competitors FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_kb_participace_updated_at BEFORE UPDATE ON kb_participace FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_kb_rozpocet_updated_at BEFORE UPDATE ON kb_rozpocet FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_kb_support_updated_at BEFORE UPDATE ON kb_support FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_kb_general_updated_at BEFORE UPDATE ON kb_general FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- KOMENTÁŘE A METADATA
-- =====================================================

COMMENT ON TABLE kb_onboarding_pruzkumy IS 'Znalostní báze pro metodologie a postupy onboarding průzkumů';
COMMENT ON TABLE kb_best_practices_onboarding IS 'Best practices specifické pro onboarding proces';
COMMENT ON TABLE kb_sales_materials IS 'Prodejní materiály a strategie pro sales qualification';
COMMENT ON TABLE kb_competitors IS 'Analýzy konkurence a market intelligence';
COMMENT ON TABLE kb_participace IS 'Metodiky a postupy pro participativní procesy';
COMMENT ON TABLE kb_rozpocet IS 'Pravidla a postupy pro rozpočtování projektů';
COMMENT ON TABLE kb_support IS 'Znalostní báze pro customer support';
COMMENT ON TABLE kb_general IS 'Obecné best practices sdílené napříč doménami';
