-- SQL pro vytvo<PERSON><PERSON><PERSON> tabulky best_practices se stejnou strukturou jako gdanalyst
-- Pro kompatibilitu s SupabaseVectorStore

-- Nejprve smažeme existující tabulku (pokud má špatnou strukturu)
DROP TABLE IF EXISTS best_practices;

-- Vyt<PERSON>říme tabulku se stejnou strukturou jako gdanalyst
CREATE TABLE best_practices (
    id BIGSERIAL PRIMARY KEY,
    content TEXT NOT NULL,
    metadata JSONB,
    embedding VECTOR(1536)
);

-- Zapnutí Row Level Security
ALTER TABLE best_practices ENABLE ROW LEVEL SECURITY;

-- Policies pro přístup (stejn<PERSON> jako u gdanalyst)
CREATE POLICY "Enable read access for all users" ON best_practices
    FOR SELECT USING (true);

CREATE POLICY "Enable insert access for all users" ON best_practices
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Enable update access for all users" ON best_practices
    FOR UPDATE USING (true);

CREATE POLICY "Enable delete access for all users" ON best_practices
    FOR DELETE USING (true);

-- Indexy pro rychlé vyhledávání
CREATE INDEX IF NOT EXISTS idx_best_practices_embedding 
ON best_practices USING ivfflat (embedding vector_cosine_ops) 
WITH (lists = 100);

-- Funkce pro vektorové vyhledávání (podle vzoru match_gdanalyst)
-- Poznámka: Parametry podle hintu: (filter, match_count, query_embedding)
CREATE OR REPLACE FUNCTION match_best_practices(
  filter JSONB DEFAULT '{}',
  match_count INT DEFAULT 5,
  query_embedding VECTOR(1536) DEFAULT NULL
)
RETURNS TABLE (
  id BIGINT,
  content TEXT,
  metadata JSONB,
  similarity FLOAT
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    bp.id,
    bp.content,
    bp.metadata,
    1 - (bp.embedding <=> query_embedding) as similarity
  FROM
    best_practices bp
  WHERE 
    (query_embedding IS NULL OR 1 - (bp.embedding <=> query_embedding) > 0.5)
    AND (filter = '{}' OR bp.metadata @> filter)
  ORDER BY
    similarity DESC
  LIMIT match_count;
END;
$$;

-- Komentáře pro dokumentaci
COMMENT ON TABLE best_practices IS 'Tabulka pro best practices kompatibilní s SupabaseVectorStore';
COMMENT ON COLUMN best_practices.content IS 'Textový obsah best practice';
COMMENT ON COLUMN best_practices.metadata IS 'Metadata s detaily best practice';
COMMENT ON COLUMN best_practices.embedding IS 'Vektorový embedding pro sémantické vyhledávání';
COMMENT ON FUNCTION match_best_practices IS 'Funkce pro vektorové vyhledávání best practices';

-- Test vložení ukázkového záznamu
INSERT INTO best_practices (content, metadata, embedding) VALUES (
  'Test best practice: Při nejasných požadavcích klienta použít strukturovaný dotazník',
  '{
    "context_description": "Klient měl nejasné požadavky na výzkum",
    "successful_strategy": "Použití strukturovaného dotazníku pro upřesnění potřeb",
    "associated_agent_role": "ClientOnboarder",
    "success_rating": 0.9,
    "feedback_notes": ["Klient ocenil systematický přístup"],
    "type": "best_practice"
  }',
  NULL
);

-- Informace o úspěšném vytvoření
SELECT 'Best practices tabulka úspěšně vytvořena!' as status;
