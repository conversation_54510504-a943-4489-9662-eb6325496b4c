-- SQL skript pro vytvoření tabulky best_practices podle specifikace
-- Implementuje zadání z tasks/best_practices.md

-- Vytvoření tabulky best_practices
CREATE TABLE IF NOT EXISTS best_practices (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at timestamptz DEFAULT now(),
    context_description text NOT NULL,
    successful_strategy text NOT NULL,
    associated_agent_role text NOT NULL,
    success_rating float4 DEFAULT 0.95,
    feedback_notes text[] DEFAULT '{}',
    embedding vector(1536)
);

-- Zapnutí Row Level Security
ALTER TABLE best_practices ENABLE ROW LEVEL SECURITY;

-- Vytvoření policy pro čtení (všichni mohou č<PERSON>t)
CREATE POLICY "Enable read access for all users" ON best_practices
    FOR SELECT USING (true);

-- Vytvoření policy pro vkládání (v<PERSON><PERSON>ni mohou vkládat)
CREATE POLICY "Enable insert access for all users" ON best_practices
    FOR INSERT WITH CHECK (true);

-- Vytvoření policy pro aktualizaci (všichni mohou aktualizovat)
CREATE POLICY "Enable update access for all users" ON best_practices
    FOR UPDATE USING (true);

-- Indexy pro rychlé vyhledávání
CREATE INDEX IF NOT EXISTS idx_best_practices_agent_role ON best_practices(associated_agent_role);
CREATE INDEX IF NOT EXISTS idx_best_practices_success_rating ON best_practices(success_rating);
CREATE INDEX IF NOT EXISTS idx_best_practices_created_at ON best_practices(created_at);

-- Index pro vektorové vyhledávání
CREATE INDEX IF NOT EXISTS idx_best_practices_embedding ON best_practices 
USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- Funkce pro sémantické vyhledávání podle specifikace
CREATE OR REPLACE FUNCTION match_best_practices (
  query_embedding vector(1536),
  match_threshold float,
  match_count int
)
RETURNS TABLE (
  id uuid,
  context_description text,
  successful_strategy text,
  associated_agent_role text,
  similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    bp.id,
    bp.context_description,
    bp.successful_strategy,
    bp.associated_agent_role,
    1 - (bp.embedding <=> query_embedding) as similarity
  FROM
    best_practices bp
  WHERE 1 - (bp.embedding <=> query_embedding) > match_threshold
  ORDER BY
    similarity DESC
  LIMIT match_count;
END;
$$;

-- Komentáře pro dokumentaci
COMMENT ON TABLE best_practices IS 'Tabulka pro ukládání best practices s vektorovým vyhledáváním';
COMMENT ON COLUMN best_practices.context_description IS 'Popis situace, která vedla k této zkušenosti (vektorizováno)';
COMMENT ON COLUMN best_practices.successful_strategy IS 'Popis ověřeného postupu nebo řešení';
COMMENT ON COLUMN best_practices.associated_agent_role IS 'Role agenta, např. ClientOnboarder';
COMMENT ON COLUMN best_practices.success_rating IS 'Číselné hodnocení úspěšnosti (0-1)';
COMMENT ON COLUMN best_practices.feedback_notes IS 'Pole textů pro dodatečné poznámky';
COMMENT ON COLUMN best_practices.embedding IS 'Vektorový embedding pro sémantické vyhledávání';

COMMENT ON FUNCTION match_best_practices IS 'Funkce pro sémantické vyhledávání best practices pomocí vektorů';
