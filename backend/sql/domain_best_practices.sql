-- SQL skripty pro rozš<PERSON>ření best practices o podporu domén
-- Matylda v2.0 - Domain-aware best practices

-- =====================================================
-- ROZŠÍŘENÍ EXISTUJÍCÍ TABULKY BEST PRACTICES
-- =====================================================

-- <PERSON><PERSON><PERSON><PERSON><PERSON> sloupců pro domény a kontexty
ALTER TABLE best_practices 
ADD COLUMN IF NOT EXISTS domain VARCHAR(50) DEFAULT 'default',
ADD COLUMN IF NOT EXISTS context VARCHAR(100),
ADD COLUMN IF NOT EXISTS subdomain VARCHAR(50),
ADD COLUMN IF NOT EXISTS target_audience VARCHAR(50) DEFAULT 'general',
ADD COLUMN IF NOT EXISTS complexity_level VARCHAR(20) DEFAULT 'intermediate',
ADD COLUMN IF NOT EXISTS language VARCHAR(10) DEFAULT 'cs';

-- Indexy pro rychlé vyhledávání podle domén
CREATE INDEX IF NOT EXISTS idx_best_practices_domain ON best_practices(domain);
CREATE INDEX IF NOT EXISTS idx_best_practices_context ON best_practices(context);
CREATE INDEX IF NOT EXISTS idx_best_practices_domain_context ON best_practices(domain, context);
CREATE INDEX IF NOT EXISTS idx_best_practices_category_domain ON best_practices(category, domain);

-- =====================================================
-- NOVÁ TABULKA PRO DOMAIN MAPPINGS
-- =====================================================

-- Tabulka pro mapování domén a kontextů
CREATE TABLE IF NOT EXISTS domain_contexts (
    id SERIAL PRIMARY KEY,
    domain VARCHAR(50) NOT NULL,
    context VARCHAR(100) NOT NULL,
    description TEXT,
    parent_context VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(domain, context)
);

-- Indexy pro domain_contexts
CREATE INDEX IF NOT EXISTS idx_domain_contexts_domain ON domain_contexts(domain);
CREATE INDEX IF NOT EXISTS idx_domain_contexts_active ON domain_contexts(is_active);

-- =====================================================
-- NOVÁ TABULKA PRO CROSS-DOMAIN RELATIONSHIPS
-- =====================================================

-- Tabulka pro vztahy mezi doménami
CREATE TABLE IF NOT EXISTS domain_relationships (
    id SERIAL PRIMARY KEY,
    source_domain VARCHAR(50) NOT NULL,
    target_domain VARCHAR(50) NOT NULL,
    relationship_type VARCHAR(30) NOT NULL, -- 'shared', 'inherited', 'referenced'
    strength FLOAT DEFAULT 1.0, -- síla vztahu 0.0-1.0
    is_bidirectional BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(source_domain, target_domain, relationship_type)
);

-- Index pro domain_relationships
CREATE INDEX IF NOT EXISTS idx_domain_relationships_source ON domain_relationships(source_domain);
CREATE INDEX IF NOT EXISTS idx_domain_relationships_target ON domain_relationships(target_domain);

-- =====================================================
-- NOVÁ TABULKA PRO USAGE ANALYTICS
-- =====================================================

-- Tabulka pro sledování použití best practices
CREATE TABLE IF NOT EXISTS best_practices_usage (
    id BIGSERIAL PRIMARY KEY,
    best_practice_id INTEGER REFERENCES best_practices(id),
    domain VARCHAR(50) NOT NULL,
    context VARCHAR(100),
    user_session VARCHAR(100),
    query_text TEXT,
    relevance_score FLOAT,
    was_helpful BOOLEAN,
    feedback TEXT,
    used_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexy pro usage analytics
CREATE INDEX IF NOT EXISTS idx_bp_usage_practice_id ON best_practices_usage(best_practice_id);
CREATE INDEX IF NOT EXISTS idx_bp_usage_domain ON best_practices_usage(domain);
CREATE INDEX IF NOT EXISTS idx_bp_usage_used_at ON best_practices_usage(used_at);

-- =====================================================
-- FUNKCE PRO DOMAIN-AWARE VYHLEDÁVÁNÍ
-- =====================================================

-- Funkce pro vyhledávání best practices podle domény
CREATE OR REPLACE FUNCTION search_best_practices_by_domain(
    p_query TEXT,
    p_domain VARCHAR(50) DEFAULT NULL,
    p_context VARCHAR(100) DEFAULT NULL,
    p_category VARCHAR(50) DEFAULT NULL,
    p_limit INTEGER DEFAULT 10
)
RETURNS TABLE (
    id INTEGER,
    title VARCHAR(200),
    category VARCHAR(50),
    description TEXT,
    context VARCHAR(100),
    lesson_learned TEXT,
    project_type VARCHAR(50),
    domain VARCHAR(50),
    success_metrics TEXT[],
    tags TEXT[],
    effectiveness_score FLOAT,
    usage_count INTEGER,
    relevance_score FLOAT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        bp.id,
        bp.title,
        bp.category,
        bp.description,
        bp.context,
        bp.lesson_learned,
        bp.project_type,
        bp.domain,
        bp.success_metrics,
        bp.tags,
        bp.effectiveness_score,
        bp.usage_count,
        -- Výpočet relevance na základě textové podobnosti a domény
        CASE 
            WHEN bp.domain = p_domain THEN 1.0
            WHEN bp.domain = 'shared' THEN 0.8
            WHEN EXISTS (
                SELECT 1 FROM domain_relationships dr 
                WHERE dr.source_domain = p_domain 
                AND dr.target_domain = bp.domain
            ) THEN 0.6
            ELSE 0.3
        END * (
            CASE 
                WHEN p_query IS NULL OR p_query = '' THEN 1.0
                ELSE (
                    ts_rank(
                        to_tsvector('czech', COALESCE(bp.title, '') || ' ' || COALESCE(bp.description, '') || ' ' || COALESCE(bp.lesson_learned, '')),
                        plainto_tsquery('czech', p_query)
                    ) + 0.1
                )
            END
        ) AS relevance_score
    FROM best_practices bp
    WHERE 
        (p_domain IS NULL OR bp.domain = p_domain OR bp.domain = 'shared' OR 
         EXISTS (
             SELECT 1 FROM domain_relationships dr 
             WHERE dr.source_domain = p_domain AND dr.target_domain = bp.domain
         ))
        AND (p_context IS NULL OR bp.context = p_context)
        AND (p_category IS NULL OR bp.category = p_category)
        AND (p_query IS NULL OR p_query = '' OR 
             to_tsvector('czech', COALESCE(bp.title, '') || ' ' || COALESCE(bp.description, '') || ' ' || COALESCE(bp.lesson_learned, '')) @@ plainto_tsquery('czech', p_query))
    ORDER BY relevance_score DESC, bp.effectiveness_score DESC, bp.usage_count DESC
    LIMIT p_limit;
END;
$$;

-- Funkce pro získání souvisejících domén
CREATE OR REPLACE FUNCTION get_related_domains(p_domain VARCHAR(50))
RETURNS TABLE (
    domain VARCHAR(50),
    relationship_type VARCHAR(30),
    strength FLOAT
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        dr.target_domain as domain,
        dr.relationship_type,
        dr.strength
    FROM domain_relationships dr
    WHERE dr.source_domain = p_domain
    UNION
    SELECT 
        dr.source_domain as domain,
        dr.relationship_type,
        dr.strength
    FROM domain_relationships dr
    WHERE dr.target_domain = p_domain AND dr.is_bidirectional = true
    ORDER BY strength DESC;
END;
$$;

-- =====================================================
-- TRIGGERY PRO AUTOMATICKÉ AKTUALIZACE
-- =====================================================

-- Trigger pro aktualizaci usage_count při použití
CREATE OR REPLACE FUNCTION update_best_practice_usage()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE best_practices 
    SET usage_count = usage_count + 1,
        updated_at = NOW()
    WHERE id = NEW.best_practice_id;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_best_practice_usage
    AFTER INSERT ON best_practices_usage
    FOR EACH ROW
    EXECUTE FUNCTION update_best_practice_usage();

-- Trigger pro aktualizaci updated_at v domain_contexts
CREATE TRIGGER update_domain_contexts_updated_at 
    BEFORE UPDATE ON domain_contexts 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- VÝCHOZÍ DATA PRO DOMÉNY A KONTEXTY
-- =====================================================

-- Vložení základních domén
INSERT INTO domain_contexts (domain, context, description) VALUES
('onboarding_pruzkumy', 'metodologie', 'Metodologie průzkumů a výzkumu'),
('onboarding_pruzkumy', 'kvalita', 'Zajištění kvality průzkumů'),
('onboarding_pruzkumy', 'komunikace', 'Komunikace s respondenty'),
('onboarding_pruzkumy', 'analýza', 'Analýza dat a výsledků'),

('sales_qualification', 'lead_scoring', 'Hodnocení a kvalifikace leadů'),
('sales_qualification', 'discovery', 'Objevování potřeb klientů'),
('sales_qualification', 'presentation', 'Prezentace řešení'),
('sales_qualification', 'closing', 'Uzavírání obchodů'),

('participativni_rozpocet', 'facilitace', 'Facilitace participativních procesů'),
('participativni_rozpocet', 'hodnocení', 'Hodnocení projektových návrhů'),
('participativni_rozpocet', 'komunikace', 'Komunikace s občany'),
('participativni_rozpocet', 'transparentnost', 'Zajištění transparentnosti'),

('customer_support', 'troubleshooting', 'Řešení technických problémů'),
('customer_support', 'escalation', 'Eskalace složitých případů'),
('customer_support', 'satisfaction', 'Zajištění spokojenosti zákazníků'),
('customer_support', 'knowledge_management', 'Správa znalostní báze'),

('shared', 'communication', 'Obecné komunikační principy'),
('shared', 'quality', 'Obecné principy kvality'),
('shared', 'project_management', 'Řízení projektů'),
('shared', 'legal_compliance', 'Právní compliance')
ON CONFLICT (domain, context) DO NOTHING;

-- Vložení vztahů mezi doménami
INSERT INTO domain_relationships (source_domain, target_domain, relationship_type, strength, is_bidirectional) VALUES
('onboarding_pruzkumy', 'shared', 'inherited', 0.8, false),
('sales_qualification', 'shared', 'inherited', 0.8, false),
('participativni_rozpocet', 'shared', 'inherited', 0.8, false),
('customer_support', 'shared', 'inherited', 0.8, false),

('onboarding_pruzkumy', 'participativni_rozpocet', 'shared', 0.6, true),
('sales_qualification', 'customer_support', 'shared', 0.5, true),

('onboarding_pruzkumy', 'sales_qualification', 'referenced', 0.3, false),
('participativni_rozpocet', 'customer_support', 'referenced', 0.3, false)
ON CONFLICT (source_domain, target_domain, relationship_type) DO NOTHING;

-- =====================================================
-- VIEWS PRO SNADNÝ PŘÍSTUP
-- =====================================================

-- View pro best practices s domain informacemi
CREATE OR REPLACE VIEW v_best_practices_with_domain AS
SELECT 
    bp.*,
    dc.description as context_description,
    dc.parent_context,
    COALESCE(
        (SELECT AVG(bpu.relevance_score) 
         FROM best_practices_usage bpu 
         WHERE bpu.best_practice_id = bp.id 
         AND bpu.used_at > NOW() - INTERVAL '30 days'),
        0
    ) as recent_relevance_score,
    COALESCE(
        (SELECT COUNT(*) 
         FROM best_practices_usage bpu 
         WHERE bpu.best_practice_id = bp.id 
         AND bpu.used_at > NOW() - INTERVAL '30 days'),
        0
    ) as recent_usage_count
FROM best_practices bp
LEFT JOIN domain_contexts dc ON bp.domain = dc.domain AND bp.context = dc.context;

-- View pro domain statistiky
CREATE OR REPLACE VIEW v_domain_statistics AS
SELECT 
    bp.domain,
    COUNT(*) as total_practices,
    AVG(bp.effectiveness_score) as avg_effectiveness,
    SUM(bp.usage_count) as total_usage,
    COUNT(DISTINCT bp.category) as categories_count,
    COUNT(DISTINCT bp.context) as contexts_count,
    MAX(bp.updated_at) as last_updated
FROM best_practices bp
GROUP BY bp.domain;

-- =====================================================
-- KOMENTÁŘE A DOKUMENTACE
-- =====================================================

COMMENT ON TABLE domain_contexts IS 'Definice kontextů pro jednotlivé domény';
COMMENT ON TABLE domain_relationships IS 'Vztahy a závislosti mezi doménami';
COMMENT ON TABLE best_practices_usage IS 'Sledování použití best practices pro analytics';

COMMENT ON FUNCTION search_best_practices_by_domain IS 'Pokročilé vyhledávání best practices s podporou domén a kontextů';
COMMENT ON FUNCTION get_related_domains IS 'Získání souvisejících domén pro cross-domain doporučení';

COMMENT ON VIEW v_best_practices_with_domain IS 'Rozšířený pohled na best practices s domain informacemi';
COMMENT ON VIEW v_domain_statistics IS 'Statistiky použití podle domén';
