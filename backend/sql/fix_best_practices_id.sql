-- Oprava tabulky best_practices pro kompatibilitu s SupabaseVectorStore
-- SupabaseVectorStore používá UUID místo BIGINT pro ID

-- S<PERSON>žeme tabulku a vytvoř<PERSON><PERSON> znovu s UUID
DROP TABLE IF EXISTS best_practices;

-- Vytvoříme tabulku s UUID ID (jako m<PERSON> pravděpo<PERSON>bně gdanalyst)
CREATE TABLE best_practices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    content TEXT NOT NULL,
    metadata JSONB,
    embedding VECTOR(1536)
);

-- Zapnutí Row Level Security
ALTER TABLE best_practices ENABLE ROW LEVEL SECURITY;

-- Policies pro přístup
CREATE POLICY "Enable read access for all users" ON best_practices
    FOR SELECT USING (true);

CREATE POLICY "Enable insert access for all users" ON best_practices
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Enable update access for all users" ON best_practices
    FOR UPDATE USING (true);

CREATE POLICY "Enable delete access for all users" ON best_practices
    FOR DELETE USING (true);

-- Indexy pro rychlé vyhledávání
CREATE INDEX IF NOT EXISTS idx_best_practices_embedding 
ON best_practices USING ivfflat (embedding vector_cosine_ops) 
WITH (lists = 100);

-- Funkce pro vektorové vyhledávání s UUID
CREATE OR REPLACE FUNCTION match_best_practices(
  filter JSONB DEFAULT '{}',
  match_count INT DEFAULT 5,
  query_embedding VECTOR(1536) DEFAULT NULL
)
RETURNS TABLE (
  id UUID,
  content TEXT,
  metadata JSONB,
  similarity FLOAT
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    bp.id,
    bp.content,
    bp.metadata,
    1 - (bp.embedding <=> query_embedding) as similarity
  FROM
    best_practices bp
  WHERE 
    (query_embedding IS NULL OR 1 - (bp.embedding <=> query_embedding) > 0.5)
    AND (filter = '{}' OR bp.metadata @> filter)
  ORDER BY
    similarity DESC
  LIMIT match_count;
END;
$$;

-- Test vložení ukázkového záznamu s embeddingem
INSERT INTO best_practices (content, metadata) VALUES (
  'Test best practice: Při nejasných požadavcích klienta použít strukturovaný dotazník',
  '{
    "context_description": "Klient měl nejasné požadavky na výzkum",
    "successful_strategy": "Použití strukturovaného dotazníku pro upřesnění potřeb",
    "associated_agent_role": "ClientOnboarder",
    "success_rating": 0.9,
    "feedback_notes": ["Klient ocenil systematický přístup"],
    "type": "best_practice"
  }'
);

-- Druhý test záznam
INSERT INTO best_practices (content, metadata) VALUES (
  'Test best practice: Při nízké response rate použít reminder emails',
  '{
    "context_description": "Nízká response rate v online průzkumu",
    "successful_strategy": "Poslání reminder emailů po 3 a 7 dnech zvýšilo response rate o 15%",
    "associated_agent_role": "SurveyArchitect", 
    "success_rating": 0.85,
    "feedback_notes": ["Efektivní metoda", "Doporučeno pro všechny průzkumy"],
    "type": "best_practice"
  }'
);

-- Informace o úspěšném vytvoření
SELECT 'Best practices tabulka s UUID úspěšně vytvořena!' as status;
