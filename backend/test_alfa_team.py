#!/usr/bin/env python3
"""
Test script pro Alfa Team - detailní analýza workflow a výstupů
"""

import os
import sys
import logging
from dotenv import load_dotenv
from pathlib import Path

# Přidání backend do Python path
sys.path.append(str(Path(__file__).parent))

from alfa_orchestrator import AlfaOrchestrator, BriefState
from config_loader import MatyldaConfigLoader

# Načtení konfigurace
load_dotenv()

# Nastavení detailního logování
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('alfa_team_test.log')
    ]
)
logger = logging.getLogger(__name__)

def test_config_loading():
    """Test načítání konfigurace alfa týmu"""
    print("🧪 === TEST NAČÍTÁNÍ KONFIGURACE ===")
    
    try:
        config_loader = MatyldaConfigLoader()
        
        # Test načítání agentů
        print("\n🤖 Test načítání agentů:")
        agents_config = config_loader.load_agents_config("alfa_agents.yaml")
        print(f"✅ Načteno {len(agents_config)} agentů:")
        for agent_id, config in agents_config.items():
            print(f"   - {agent_id}: {config['role']}")
            print(f"     Nástroje: {config.get('tools', [])}")
        
        # Test načítání úkolů
        print("\n📋 Test načítání úkolů:")
        tasks_config = config_loader.load_tasks_config("alfa_tasks.yaml")
        print(f"✅ Načteno {len(tasks_config)} úkolů:")
        for task_id, config in tasks_config.items():
            agent = config.get('agent', 'Bez agenta')
            print(f"   - {task_id}: Agent {agent}")
        
        # Test načítání crew
        print("\n👥 Test načítání crew:")
        crews_config = config_loader.load_crews_config("alfa_tým.yaml")
        print(f"✅ Načteno {len(crews_config)} crew:")
        for crew_id, config in crews_config.items():
            print(f"   - {crew_id}: {config.get('description', 'Bez popisu')[:50]}...")
            print(f"     Agenti: {config.get('agents', [])}")
            print(f"     Úkoly: {config.get('tasks', [])}")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při testování konfigurace: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_crew_creation():
    """Test vytvoření alfa crew"""
    print("\n🧪 === TEST VYTVOŘENÍ CREW ===")

    try:
        # Použijeme AlfaOrchestrator pro vytvoření crew
        orchestrator = AlfaOrchestrator()

        # Test vytvoření crew pomocí interní metody
        brief_state = BriefState()
        crew = orchestrator._create_alfa_crew(
            user_message="Test zpráva",
            chat_history=[],
            brief_state=brief_state
        )

        if crew:
            print(f"✅ Crew úspěšně vytvořena:")
            print(f"   👥 Agenti: {len(crew.agents)}")
            print(f"   📋 Úkoly: {len(crew.tasks)}")
            print(f"   🔄 Proces: {crew.process}")
            print(f"   📢 Verbose: {crew.verbose}")

            # Detail agentů
            print("\n🤖 Detaily agentů:")
            for i, agent in enumerate(crew.agents, 1):
                print(f"   {i}. {agent.role}")
                print(f"      🎯 Cíl: {agent.goal[:80]}...")
                print(f"      🔧 Nástroje: {len(agent.tools)}")
                print(f"      📢 Verbose: {agent.verbose}")

            # Detail úkolů
            print("\n📋 Detaily úkolů:")
            for i, task in enumerate(crew.tasks, 1):
                print(f"   {i}. {task.description[:80]}...")
                print(f"      🤖 Agent: {task.agent.role}")
                print(f"      📤 Expected output: {task.expected_output[:50]}...")

            return crew
        else:
            print("❌ Nepodařilo se vytvořit crew")
            return None

    except Exception as e:
        print(f"❌ Chyba při vytváření crew: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_orchestrator():
    """Test alfa orchestrátoru"""
    print("\n🧪 === TEST ALFA ORCHESTRÁTORU ===")
    
    try:
        # Vytvoření orchestrátoru
        orchestrator = AlfaOrchestrator()
        print("✅ Alfa orchestrátor vytvořen")
        
        # Test zpráva
        test_message = "Chci udělat průzkum spokojenosti občanů v Praze 21, zaměřený na životní prostředí, MHD a dopravu."
        test_brief = BriefState()
        
        print(f"\n📝 Test zpráva: {test_message}")
        print(f"📊 Počáteční brief state: {test_brief.model_dump()}")
        
        # Spuštění orchestrace
        print("\n🎬 Spouštím orchestraci...")
        result = orchestrator.process_message(
            user_message=test_message,
            chat_history=[],
            brief_state=test_brief,
            session_id="test-session-123"
        )
        
        print("\n🎯 === VÝSLEDEK ORCHESTRACE ===")
        print(f"✅ Orchestrace dokončena")
        print(f"📝 Chat response ({len(result.chat_response)} znaků):")
        print(f"   {result.chat_response[:200]}...")
        print(f"📊 Canvas content ({len(result.canvas_content)} znaků):")
        print(f"   {result.canvas_content[:200]}...")
        print(f"🏁 Is complete: {result.is_complete}")
        print(f"📈 Completion: {result.completion_percentage}%")
        print(f"🆔 Session ID: {result.session_id}")
        
        return result
        
    except Exception as e:
        print(f"❌ Chyba při testování orchestrátoru: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_individual_agents():
    """Test jednotlivých agentů"""
    print("\n🧪 === TEST JEDNOTLIVÝCH AGENTŮ ===")
    
    try:
        config_loader = MatyldaConfigLoader()
        
        # Test research_strategist_v1
        print("\n🧠 Test Research Strategist:")
        strategist = config_loader.create_agent("research_strategist_v1", config_file="alfa_agents.yaml")
        if strategist:
            print(f"✅ Strategist vytvořen: {strategist.role}")
            print(f"   🎯 Cíl: {strategist.goal[:100]}...")
            print(f"   🔧 Nástroje: {len(strategist.tools)}")
        else:
            print("❌ Nepodařilo se vytvořit strategist")
        
        # Test communication_expert_v1
        print("\n💬 Test Communication Expert:")
        communicator = config_loader.create_agent("communication_expert_v1", config_file="alfa_agents.yaml")
        if communicator:
            print(f"✅ Communicator vytvořen: {communicator.role}")
            print(f"   🎯 Cíl: {communicator.goal[:100]}...")
            print(f"   🔧 Nástroje: {len(communicator.tools)}")
        else:
            print("❌ Nepodařilo se vytvořit communicator")
        
        # Test data_architect_v1
        print("\n🏗️ Test Data Architect:")
        architect = config_loader.create_agent("data_architect_v1", config_file="alfa_agents.yaml")
        if architect:
            print(f"✅ Architect vytvořen: {architect.role}")
            print(f"   🎯 Cíl: {architect.goal[:100]}...")
            print(f"   🔧 Nástroje: {len(architect.tools)}")
        else:
            print("❌ Nepodařilo se vytvořit architect")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při testování agentů: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Hlavní test funkce"""
    print("🔬 === DETAILNÍ TEST ALFA TÝMU ===")
    print("Analyzuje workflow, prompty a výstupy agentů")
    print("=" * 60)
    
    # Test 1: Načítání konfigurace
    if not test_config_loading():
        print("❌ Test konfigurace selhal, ukončuji")
        return
    
    # Test 2: Vytvoření crew
    crew = test_crew_creation()
    if not crew:
        print("❌ Test vytvoření crew selhal, ukončuji")
        return
    
    # Test 3: Jednotliví agenti
    if not test_individual_agents():
        print("❌ Test agentů selhal")
    
    # Test 4: Orchestrátor
    result = test_orchestrator()
    if not result:
        print("❌ Test orchestrátoru selhal")
        return
    
    print("\n" + "=" * 60)
    print("🎉 VŠECHNY TESTY DOKONČENY")
    print("📄 Detailní logy uloženy do: alfa_team_test.log")
    print("💡 Analyzuj logy pro identifikaci problémů s kvalitou výstupů")

if __name__ == "__main__":
    main()
