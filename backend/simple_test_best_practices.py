#!/usr/bin/env python3
"""
Jednoduchý test best practices tabulky
"""

import os
from supabase import create_client, Client
from dotenv import load_dotenv

load_dotenv()

def simple_test():
    """Jednoduchý test tabulky"""
    
    url = os.getenv('SUPABASE_URL')
    key = os.getenv('SUPABASE_KEY')
    
    supabase: Client = create_client(url, key)
    print("✅ Připojen k Supabase")
    
    # Test 1: Čten<PERSON> z tabulky
    print("\n📖 Test 1: Čten<PERSON> z tabulky...")
    try:
        result = supabase.table('best_practices').select('*').execute()
        print(f"✅ Čtení úspě<PERSON>n<PERSON>, nalezeno {len(result.data)} záznamů")
        for record in result.data:
            print(f"  - ID: {record.get('id', 'N/A')}")
    except Exception as e:
        print(f"❌ Chyba při čten<PERSON>: {e}")
    
    # Test 2: <PERSON><PERSON><PERSON><PERSON><PERSON> bez embedding
    print("\n📝 Test 2: Vložení záznamu bez embedding...")
    try:
        test_data = {
            'context_description': 'Test situace pro ověření funkcionality',
            'successful_strategy': 'Test strategie - použití strukturovaného přístupu',
            'associated_agent_role': 'TestAgent',
            'success_rating': 0.9,
            'feedback_notes': ['Test poznámka', 'Úspěšný test']
        }
        
        result = supabase.table('best_practices').insert(test_data).execute()
        
        if result.data:
            test_id = result.data[0]['id']
            print(f"✅ Vložení úspěšné, ID: {test_id}")
            
            # Test 3: Čtení vloženého záznamu
            print("\n🔍 Test 3: Čtení vloženého záznamu...")
            read_result = supabase.table('best_practices').select('*').eq('id', test_id).execute()
            if read_result.data:
                record = read_result.data[0]
                print(f"✅ Záznam nalezen:")
                print(f"  - Kontext: {record['context_description'][:50]}...")
                print(f"  - Strategie: {record['successful_strategy'][:50]}...")
                print(f"  - Agent: {record['associated_agent_role']}")
                print(f"  - Rating: {record['success_rating']}")
                print(f"  - Poznámky: {record['feedback_notes']}")
            
            # Test 4: Smazání test záznamu
            print("\n🗑️ Test 4: Smazání test záznamu...")
            delete_result = supabase.table('best_practices').delete().eq('id', test_id).execute()
            print("✅ Test záznam smazán")
            
        else:
            print("❌ Vložení selhalo - žádná data")
            
    except Exception as e:
        print(f"❌ Chyba při vkládání: {e}")
    
    # Test 5: Test funkce match_best_practices
    print("\n🔧 Test 5: Test funkce match_best_practices...")
    try:
        # Dummy embedding pro test
        dummy_embedding = [0.1] * 1536
        
        result = supabase.rpc('match_best_practices', {
            'query_embedding': dummy_embedding,
            'match_threshold': 0.5,
            'match_count': 5
        }).execute()
        
        print(f"✅ Funkce match_best_practices funguje, nalezeno {len(result.data)} výsledků")
        
    except Exception as e:
        print(f"❌ Funkce match_best_practices nefunguje: {e}")
    
    print("\n🎉 Test dokončen!")

if __name__ == "__main__":
    print("🧪 Jednoduchý test Best Practices tabulky...")
    simple_test()
