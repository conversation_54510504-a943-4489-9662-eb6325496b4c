# Konfigurace znalostních b<PERSON><PERSON>í podle domén
# Izolované znalostní báze pro každou doménu Matylda platformy

# =====================================================
# DOMÉNA: Onboarding Průzkumů
# =====================================================
onboarding_pruzkumy:
  knowledge_bases:
    - id: "metodologie_pruzkumy"
      table_name: "kb_onboarding_pruzkumy"
      query_function: "match_onboarding_pruzkumy"
      description: "Metodologie a postupy pro průzkumy spokojenosti"
      categories:
        - "metodologie"
        - "průzkumy"
        - "spokojenost"
        - "občané"
      
    - id: "best_practices_onboarding"
      table_name: "kb_best_practices_onboarding"
      query_function: "match_best_practices_onboarding"
      description: "Best practices specifické pro onboarding proces"
      categories:
        - "best_practices"
        - "onboarding"
        - "proces"
        - "kvalita"
    
    - id: "statisticke_metody"
      table_name: "kb_statistika"
      query_function: "match_statistika"
      description: "Statistické metody a analýzy dat"
      categories:
        - "statistika"
        - "analýza"
        - "data"
        - "metody"

# =====================================================
# DOMÉNA: Sales Qualification
# =====================================================
sales_qualification:
  knowledge_bases:
    - id: "sales_materials"
      table_name: "kb_sales_materials"
      query_function: "match_sales_materials"
      description: "Prodejní materiály a strategie"
      categories:
        - "sales"
        - "prodej"
        - "strategie"
        - "materiály"
    
    - id: "competitor_analysis"
      table_name: "kb_competitors"
      query_function: "match_competitors"
      description: "Analýza konkurence a market intelligence"
      categories:
        - "konkurence"
        - "market"
        - "analýza"
        - "intelligence"
    
    - id: "lead_scoring"
      table_name: "kb_lead_scoring"
      query_function: "match_lead_scoring"
      description: "Metodiky hodnocení a kvalifikace leadů"
      categories:
        - "leads"
        - "scoring"
        - "kvalifikace"
        - "hodnocení"

# =====================================================
# DOMÉNA: Participativní Rozpočet
# =====================================================
participativni_rozpocet:
  knowledge_bases:
    - id: "participace_metodiky"
      table_name: "kb_participace"
      query_function: "match_participace"
      description: "Metodiky participativních procesů"
      categories:
        - "participace"
        - "metodiky"
        - "procesy"
        - "občané"
    
    - id: "rozpocet_pravidla"
      table_name: "kb_rozpocet"
      query_function: "match_rozpocet"
      description: "Pravidla a postupy rozpočtování"
      categories:
        - "rozpočet"
        - "pravidla"
        - "postupy"
        - "finance"
    
    - id: "hodnotici_kriteria"
      table_name: "kb_hodnoceni"
      query_function: "match_hodnoceni"
      description: "Kritéria a postupy hodnocení projektů"
      categories:
        - "hodnocení"
        - "kritéria"
        - "projekty"
        - "výběr"

# =====================================================
# DOMÉNA: Customer Support
# =====================================================
customer_support:
  knowledge_bases:
    - id: "knowledge_base"
      table_name: "kb_support"
      query_function: "match_support"
      description: "Znalostní báze pro zákaznickou podporu"
      categories:
        - "support"
        - "podpora"
        - "zákazníci"
        - "řešení"
    
    - id: "troubleshooting"
      table_name: "kb_troubleshooting"
      query_function: "match_troubleshooting"
      description: "Postupy řešení problémů a troubleshooting"
      categories:
        - "troubleshooting"
        - "problémy"
        - "řešení"
        - "postupy"
    
    - id: "faq_database"
      table_name: "kb_faq"
      query_function: "match_faq"
      description: "Často kladené otázky a odpovědi"
      categories:
        - "faq"
        - "otázky"
        - "odpovědi"
        - "časté"

# =====================================================
# SPOLEČNÉ ZNALOSTNÍ BÁZE
# =====================================================
shared:
  knowledge_bases:
    - id: "general_best_practices"
      table_name: "kb_general"
      query_function: "match_general"
      description: "Obecné best practices sdílené napříč doménami"
      categories:
        - "best_practices"
        - "obecné"
        - "sdílené"
        - "kvalita"
    
    - id: "communication_templates"
      table_name: "kb_communication"
      query_function: "match_communication"
      description: "Šablony a vzory komunikace"
      categories:
        - "komunikace"
        - "šablony"
        - "vzory"
        - "texty"
    
    - id: "legal_compliance"
      table_name: "kb_legal"
      query_function: "match_legal"
      description: "Právní požadavky a compliance"
      categories:
        - "právní"
        - "compliance"
        - "požadavky"
        - "zákon"

# =====================================================
# KONFIGURACE VYHLEDÁVÁNÍ
# =====================================================
search_config:
  # Výchozí parametry vyhledávání
  default_similarity_threshold: 0.78
  default_result_count: 5
  max_result_count: 20
  
  # Váhy pro různé typy obsahu
  content_weights:
    title: 1.5
    description: 1.2
    content: 1.0
    metadata: 0.8
  
  # Filtry podle kategorií
  category_filters:
    metodologie: ["metodologie", "postupy", "procesy"]
    best_practices: ["best_practices", "kvalita", "doporučení"]
    analýza: ["analýza", "data", "statistika"]
    komunikace: ["komunikace", "texty", "šablony"]

# =====================================================
# METADATA A TAGGING
# =====================================================
metadata_schema:
  # Povinná metadata
  required_fields:
    - "domain"
    - "category"
    - "created_at"
    - "language"
  
  # Volitelná metadata
  optional_fields:
    - "author"
    - "version"
    - "tags"
    - "difficulty_level"
    - "target_audience"
    - "last_updated"
    - "source"
    - "related_documents"
  
  # Standardní hodnoty
  default_values:
    language: "cs"
    difficulty_level: "intermediate"
    target_audience: "general"

# =====================================================
# IZOLACE A BEZPEČNOST
# =====================================================
isolation_config:
  # Striktní izolace domén
  strict_domain_isolation: true
  
  # Povolené cross-domain přístupy
  cross_domain_access:
    onboarding_pruzkumy:
      - "shared"
    sales_qualification:
      - "shared"
    participativni_rozpocet:
      - "shared"
    customer_support:
      - "shared"
  
  # Zabezpečení přístupu
  access_control:
    require_domain_context: true
    log_cross_domain_access: true
    audit_search_queries: true

# =====================================================
# MONITORING A ANALYTICS
# =====================================================
monitoring:
  # Metriky výkonu
  performance_metrics:
    - "search_latency"
    - "result_relevance"
    - "cache_hit_rate"
    - "domain_usage"
  
  # Logging
  logging:
    log_level: "INFO"
    log_search_queries: true
    log_results_count: true
    log_domain_switches: true
  
  # Alerty
  alerts:
    slow_queries_threshold: 2.0  # sekundy
    low_relevance_threshold: 0.5
    high_error_rate_threshold: 0.05

# =====================================================
# CACHE KONFIGURACE
# =====================================================
cache_config:
  # Redis cache pro výsledky
  enable_result_cache: true
  cache_ttl: 3600  # 1 hodina
  max_cache_size: 1000  # počet cached queries
  
  # Embedding cache
  enable_embedding_cache: true
  embedding_cache_ttl: 86400  # 24 hodin
  
  # Invalidace cache
  auto_invalidate_on_update: true
  manual_invalidation_endpoint: true

# =====================================================
# METADATA
# =====================================================
metadata:
  version: "2.0"
  created: "2025-07-10"
  description: "Konfigurace izolovaných znalostních bází pro Matylda v2.0"
  author: "Augment Agent"
  last_updated: "2025-07-10"
