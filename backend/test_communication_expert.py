#!/usr/bin/env python3
"""
Test Communication Expert - ověření kvality výstupů
"""

import os
import sys
import logging
from dotenv import load_dotenv
from pathlib import Path

# Přidání backend do Python path
sys.path.append(str(Path(__file__).parent))

from config_loader import MatyldaConfigLoader

# Načtení konfigurace
load_dotenv()

# Nastavení logování
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_communication_expert():
    """Test komunikačního experta"""
    print("🧪 === TEST COMMUNICATION EXPERT ===")
    
    try:
        config_loader = MatyldaConfigLoader()
        
        # Vytvoření agenta
        agent = config_loader.create_agent("communication_expert_v1", config_file="alfa_agents.yaml")
        if not agent:
            print("❌ Nepodařilo se vytvořit communication expert")
            return
        
        print(f"✅ Agent vytvořen: {agent.role}")
        print(f"🎯 Cíl: {agent.goal[:100]}...")
        
        # Simulace JSON vstupu od stratéga
        strategist_json = """{
  "action": "SUGGEST_AND_CONFIRM",
  "payload": {
    "suggestion_text": "Na základě analýzy doporučujeme rozšířit průzkum o témata jako 'Bezpečnost' a 'Kvalita služeb', protože tato témata mohou mít silný vliv na celkovou spokojenost občanů.",
    "suggestion_short": "Přidání témat Bezpečnost a Služby"
  },
  "brief_state": {
    "project_goal": "Zjistit spokojenost občanů MČ Praha 21",
    "target_audience": "Obyvatelé MČ Praha 21",
    "survey_topics": ["Životní prostředí", "MHD", "Doprava", "[Navrženo: Bezpečnost]", "[Navrženo: Kvalita služeb]"],
    "demographics": ["Věk", "Vzdělání", "Pohlaví"],
    "localization": "Rozdělení na 4 logické části",
    "timeline": "Září",
    "technical_output": "Limesurvey .lss soubor"
  }
}"""
        
        # Vytvoření úkolu
        agents = {"communication_expert_v1": agent}
        task = config_loader.create_task(
            "task_generate_client_response",
            agents,
            config_file="alfa_tasks.yaml",
            strategist_output=strategist_json,
            user_input="Chci udělat průzkum spokojenosti občanů v Praze 21",
            chat_history="",
            brief_state="{}"
        )
        
        if not task:
            print("❌ Nepodařilo se vytvořit úkol")
            return
        
        print(f"✅ Úkol vytvořen: {task.description[:100]}...")
        
        # Spuštění úkolu pomocí crew
        print("\n🎬 Spouštím úkol...")
        from crewai import Crew, Process

        crew = Crew(
            agents=[agent],
            tasks=[task],
            process=Process.sequential,
            verbose=True
        )

        result = crew.kickoff()

        print("\n🎯 === VÝSLEDEK ===")
        print(f"📝 Výstup ({len(str(result))} znaků):")
        print("=" * 60)
        print(str(result))
        print("=" * 60)
        
        # Analýza kvality
        result_str = str(result)
        has_structure = "## 📋" in result_str and "## 💡" in result_str and "## 🎯" in result_str and "## ❓" in result_str
        has_content = len(result_str) > 200
        is_professional = "doporučujeme" in result_str.lower() or "navrhujeme" in result_str.lower()
        
        print(f"\n📊 Analýza kvality:")
        print(f"✅ Má požadovanou strukturu: {has_structure}")
        print(f"✅ Má dostatečný obsah: {has_content}")
        print(f"✅ Je profesionální: {is_professional}")
        
        if has_structure and has_content and is_professional:
            print("🎉 Communication Expert funguje výborně!")
        else:
            print("⚠️ Communication Expert potřebuje doladění")
        
        return result
        
    except Exception as e:
        print(f"❌ Chyba při testování: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_communication_expert()
