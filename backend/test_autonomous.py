#!/usr/bin/env python3
"""
Test autonomního orchestrátoru bez závislostí na crewai
"""

import logging
from typing import Dict, Any
import uuid
from datetime import datetime

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MockAutonomousOrchestrator:
    """
    Mock implementace autonomního orchestrátoru pro testování
    """
    
    def process_request(self, initial_request: str, **kwargs) -> Dict[str, Any]:
        """
        Mock zpracování požadavku - simuluje autonomní myšlení
        """
        logger.info(f"🚀 Mock autonomní zpracování: {initial_request}")
        
        session_id = f"autonomous-{uuid.uuid4().hex[:8]}"
        
        # Simulace autonomního myšlení
        request_lower = initial_request.lower()
        if any(word in request_lower for word in ["anketa", "anketu", "průzkum", "výzkum", "spokojenost"]):
            return self._handle_survey_request(initial_request, session_id)
        else:
            return self._handle_generic_request(initial_request, session_id)
    
    def _handle_survey_request(self, request: str, session_id: str) -> Dict[str, Any]:
        """Zpracování požadavku na anketu/průzkum"""
        
        # Autonomní analýza požadavku
        chat_response = """Rozumím, že chcete vytvořit anketu o spokojenosti občanů v Praze 21. 

Na základě mých zkušeností s podobnými projekty doporučuji zaměřit se na tyto klíčové oblasti:

1. **Doprava a mobilita** - MHD, parkování, cyklostezky
2. **Životní prostředí** - zeleň, čistota, hluk
3. **Veřejné služby** - úřady, zdravotnictví, školství
4. **Bezpečnost** - veřejný pořádek, osvětlení
5. **Komunita** - kulturní akce, sport, sousedské vztahy

Připravil jsem pro vás kompletní návrh struktury ankety včetně konkrétních otázek. Detaily najdete v projektovém plátně."""

        canvas_content = """# 📋 Návrh Ankety - Spokojenost Občanů Praha 21

## 🎯 Cíl Průzkumu
Zjistit úroveň spokojenosti občanů městské části Praha 21 s veřejnými službami a kvalitou života pro podporu strategického plánování a prioritizaci investic.

## 👥 Cílová Skupina
- **Primární:** Obyvatelé Praha 21 (18+ let)
- **Velikost vzorku:** 400-500 respondentů
- **Metoda:** Online + papírová forma

## 📊 Struktura Ankety

### Úvodní Sekce
**Úvod:** "Vážení občané, Praha 21 se snaží neustále zlepšovat kvalitu života v naší městské části. Vaše názory jsou pro nás klíčové."

### 1. Doprava a Mobilita (5 otázek)
- Spokojenost s MHD spojením (škála 1-5)
- Hodnocení stavu místních komunikací
- Dostupnost parkování
- Kvalita cyklostezek
- Nejproblematičtější dopravní místa (otevřená)

### 2. Životní Prostředí (4 otázky)
- Spokojenost s množstvím zeleně
- Hodnocení čistoty veřejných prostranství
- Problém s hlukem
- Kvalita ovzduší

### 3. Veřejné Služby (4 otázky)
- Dostupnost úřadů a jejich služeb
- Spokojenost se školskými zařízeními
- Hodnocení zdravotnických služeb
- Kulturní a sportovní vyžití

### 4. Bezpečnost (3 otázky)
- Pocit bezpečí ve dne/v noci
- Kvalita veřejného osvětlení
- Hodnocení práce městské policie

### 5. Demografické údaje (3 otázky)
- Věk (kategorie)
- Délka bydlení v Praha 21
- Část městské části

## 📈 Metodologie
- **Doba sběr:** 3 týdny
- **Distribuce:** Web Praha 21, sociální sítě, papírová forma v úřadech
- **Analýza:** Deskriptivní statistiky, křížové tabulky
- **Reporting:** Exekutivní shrnutí + detailní analýza

## 💰 Odhadovaný Rozpočet
- **Celkem:** 15-20 tis. Kč
- **Breakdown:** Online platforma (5k), tisk (3k), analýza (7k), report (5k)

## ⏱️ Časový Harmonogram
1. **Týden 1:** Finalizace otázek, příprava platformy
2. **Týden 2-4:** Sběr dat
3. **Týden 5:** Analýza a zpracování
4. **Týden 6:** Finální report a prezentace

## 🎯 Očekávané Výstupy
- Detailní analýza spokojenosti po oblastech
- Identifikace prioritních problémů
- Doporučení pro strategické plánování
- Srovnání s předchozími průzkumy (pokud existují)

---
*Návrh připraven autonomním systémem Matylda v2.0*
*Aktualizováno: {datetime.now().strftime('%Y-%m-%d %H:%M')}*"""

        return {
            "session_id": session_id,
            "chat_response": chat_response,
            "canvas_content": canvas_content,
            "domain": "onboarding_pruzkumy",
            "domain_name": "Onboarding Průzkumů",
            "is_complete": True,
            "completion_percentage": 100,
            "status": "completed"
        }
    
    def _handle_generic_request(self, request: str, session_id: str) -> Dict[str, Any]:
        """Zpracování obecného požadavku"""
        
        chat_response = f"""Rozumím vašemu požadavku: "{request}"

Jako autonomní projektový manažer analyzuji váš požadavek a připravím strukturovaný přístup k jeho řešení. 

Potřebuji však více informací, abych mohl vytvořit kompletní řešení. Můžete mi prosím upřesnit:
1. Jaký je hlavní cíl tohoto projektu?
2. Kdo je cílová skupina?
3. Jaký je časový rámec?
4. Existují nějaká omezení nebo specifické požadavky?

Na základě těchto informací vám připravím detailní návrh řešení."""

        canvas_content = f"""# 📋 Analýza Požadavku

## 🎯 Původní Požadavek
{request}

## 🔍 Potřebné Informace
- **Cíl projektu:** Zatím neurčeno
- **Cílová skupina:** Zatím neurčeno  
- **Časový rámec:** Zatím neurčeno
- **Rozpočet:** Zatím neurčeno
- **Specifické požadavky:** Zatím neurčeno

## 📊 Progres Dokončení: 20%

---
*Aktualizováno: {datetime.now().strftime('%Y-%m-%d %H:%M')}*"""

        return {
            "session_id": session_id,
            "chat_response": chat_response,
            "canvas_content": canvas_content,
            "domain": "universal",
            "domain_name": "Univerzální",
            "is_complete": False,
            "completion_percentage": 20,
            "status": "active"
        }

if __name__ == "__main__":
    # Test
    orchestrator = MockAutonomousOrchestrator()
    
    # Test 1: Anketa
    print("=== TEST 1: Anketa ===")
    result1 = orchestrator.process_request("rád bych udělal anketu o spokojenosti občanů v Praze 21")
    print(f"Chat: {result1['chat_response'][:100]}...")
    print(f"Canvas: {len(result1['canvas_content'])} znaků")
    print(f"Domain: {result1['domain']}")
    print()
    
    # Test 2: Obecný požadavek
    print("=== TEST 2: Obecný požadavek ===")
    result2 = orchestrator.process_request("potřebuji pomoct s marketingovou strategií")
    print(f"Chat: {result2['chat_response'][:100]}...")
    print(f"Canvas: {len(result2['canvas_content'])} znaků")
    print(f"Domain: {result2['domain']}")
