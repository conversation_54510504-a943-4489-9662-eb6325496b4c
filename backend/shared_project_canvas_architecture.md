# 🎨 Architektura Sdíleného Projektového Plátna

## 📋 Přehled Konceptu

Sdílené projektové plátno je "Single Source of Truth" pro všechny agenty v týmu. Každý agent může číst a aktualizovat plátno, p<PERSON><PERSON><PERSON><PERSON>ž systém automaticky verzuje změny a poskytuje dva pohledy:

- **Interní pl<PERSON>** (debug mode): Kompletní stav s agent activities, reasoning, mezivýstupy
- **Klientské plátno** (production mode): <PERSON><PERSON><PERSON>, profesionální přehled pro klienta

## 🏗️ Architektura Komponent

### 1. SharedProjectCanvas (Core)
```python
class SharedProjectCanvas:
    """
    Centrální úložiště stavu projektu s verzováním a multi-view podporou
    """
    
    # Datové struktury
    internal_state: Dict[str, Any]      # Kompletní interní stav
    client_state: Dict[str, Any]        # Čištěná verze pro klienta
    agent_activities: List[AgentActivity] # Real-time aktivity agentů
    version_history: List[CanvasVersion] # Historie změn
    metadata: CanvasMetadata            # Metadata projektu
    
    # Core metody
    def update_by_agent(agent_id: str, section: str, content: str, reasoning: str = None)
    def get_client_view() -> str        # Čistý Markdown pro klienta
    def get_debug_view() -> str         # Kompletní stav s debug info
    def get_agent_activities() -> List[AgentActivity]
    def create_version_snapshot() -> str
    def rollback_to_version(version_id: str) -> bool
```

### 2. AgentActivityTracker
```python
class AgentActivityTracker:
    """
    Sledování real-time aktivit agentů podobné ChatGPT reasoning módu
    """
    
    def log_activity(agent: str, activity: str, status: ActivityStatus, metadata: Dict = None)
    def get_current_activities() -> List[AgentActivity]
    def get_activity_history(agent_id: str = None) -> List[AgentActivity]
    def clear_completed_activities()
    
class AgentActivity:
    agent_id: str
    activity_type: ActivityType  # THINKING, RESEARCHING, WRITING, UPDATING
    description: str
    status: ActivityStatus       # STARTED, IN_PROGRESS, COMPLETED, FAILED
    timestamp: datetime
    duration: Optional[float]
    metadata: Dict[str, Any]
```

### 3. CanvasSection (Strukturované sekce)
```python
class CanvasSection:
    """
    Jednotlivé sekce projektového plátna s vlastníky a stavy
    """
    
    section_id: str
    title: str
    content: str
    owner_agent: str
    status: SectionStatus        # DRAFT, REVIEW, APPROVED, FINAL
    last_updated: datetime
    version: int
    dependencies: List[str]      # Závislosti na jiných sekcích
    
class SectionStatus(Enum):
    DRAFT = "draft"              # ⏳ Rozpracováno
    REVIEW = "review"            # 🔄 K posouzení
    APPROVED = "approved"        # ✅ Schváleno
    FINAL = "final"              # 🏁 Finální
    NEEDS_UPDATE = "needs_update" # ❓ Vyžaduje aktualizaci
```

## 📊 Datové Struktury

### Canvas State Schema
```python
class CanvasState(BaseModel):
    # Metadata
    project_id: str
    project_name: str
    created_at: datetime
    last_updated: datetime
    current_phase: ProjectPhase
    
    # Obsah
    sections: Dict[str, CanvasSection]
    
    # Standardní sekce pro výzkumné projekty
    STANDARD_SECTIONS = {
        "project_overview": "🎯 Přehled Projektu",
        "objectives": "📋 Cíle a Rozsah",
        "methodology": "🔬 Metodologie",
        "timeline": "🗓️ Časový Harmonogram", 
        "deliverables": "📦 Výstupy",
        "technical_specs": "⚙️ Technické Specifikace",
        "recommendations": "💡 Doporučení",
        "next_steps": "➡️ Další Kroky"
    }

class ProjectPhase(Enum):
    SCOPING = "scoping"          # Fáze 1: Slaďování zadání
    RESEARCH = "research"        # Fáze 2: Hloubková rešerše
    DESIGN = "design"           # Fáze 3: Návrh řešení
    APPROVAL = "approval"       # Fáze 4: Schvalování
    DELIVERY = "delivery"       # Fáze 5: Dodání
```

### Agent Activity Schema
```python
class ActivityType(Enum):
    THINKING = "thinking"        # 🧠 Přemýšlí
    RESEARCHING = "researching"  # 🔍 Vyhledává
    WRITING = "writing"         # ✍️ Píše
    UPDATING = "updating"       # 🔄 Aktualizuje
    REVIEWING = "reviewing"     # 👀 Kontroluje
    WAITING = "waiting"         # ⏳ Čeká

class ActivityStatus(Enum):
    STARTED = "started"         # 🚀 Spuštěno
    IN_PROGRESS = "in_progress" # ⏳ Probíhá
    COMPLETED = "completed"     # ✅ Dokončeno
    FAILED = "failed"          # ❌ Selhalo
    PAUSED = "paused"          # ⏸️ Pozastaveno
```

## 🔄 Workflow Integration

### Agent Integration Pattern
```python
class BaseAgent:
    def __init__(self, canvas: SharedProjectCanvas, activity_tracker: AgentActivityTracker):
        self.canvas = canvas
        self.activity_tracker = activity_tracker
        self.agent_id = self.__class__.__name__
    
    def update_canvas_section(self, section_id: str, content: str, reasoning: str = None):
        """Standardní metoda pro aktualizaci plátna"""
        self.activity_tracker.log_activity(
            agent=self.agent_id,
            activity="Aktualizuji sekci " + section_id,
            status=ActivityStatus.STARTED
        )
        
        self.canvas.update_by_agent(
            agent_id=self.agent_id,
            section=section_id,
            content=content,
            reasoning=reasoning
        )
        
        self.activity_tracker.log_activity(
            agent=self.agent_id,
            activity="Sekce " + section_id + " aktualizována",
            status=ActivityStatus.COMPLETED
        )
```

### Orchestrator Integration
```python
class AlfaOrchestrator:
    def __init__(self):
        self.canvas = SharedProjectCanvas()
        self.activity_tracker = AgentActivityTracker()
        
    def process_message(self, user_message: str, session_id: str):
        # Inicializace plátna pro novou session
        if not self.canvas.exists(session_id):
            self.canvas.initialize_project(session_id, user_message)
        
        # Spuštění agentů s sdíleným plátnem
        for agent in self.agents:
            agent.canvas = self.canvas
            agent.activity_tracker = self.activity_tracker
        
        # Orchestrace s real-time updates
        result = self.run_crew_with_canvas()
        
        return {
            "chat_response": result.final_output,
            "canvas_content": self.canvas.get_client_view(),
            "debug_canvas": self.canvas.get_debug_view(),
            "agent_activities": self.activity_tracker.get_current_activities()
        }
```

## 🎨 Frontend Integration

### WebSocket Events
```javascript
// Real-time updates z backendu
socket.on('agent_activity', (activity) => {
    showAgentActivity(activity.agent, activity.description, activity.status);
});

socket.on('canvas_updated', (data) => {
    updateProjectCanvas(data.client_view);
    if (debugMode) {
        updateDebugCanvas(data.debug_view);
    }
});

socket.on('phase_changed', (phase) => {
    updateProjectPhase(phase);
});
```

### UI Components
```javascript
// Agent Activity Component (podobné ChatGPT reasoning)
function showAgentActivity(agent, activity, status) {
    const indicator = createActivityIndicator({
        icon: getAgentIcon(agent),
        text: activity,
        status: getStatusIcon(status),
        timestamp: new Date()
    });
    
    // Animace probliknutí
    animateActivity(indicator);
}

// Debug/Production Toggle
function toggleDebugMode() {
    const debugMode = !getCurrentDebugMode();
    setDebugMode(debugMode);
    
    if (debugMode) {
        showDebugCanvas();
        showAgentActivities();
        showVersionHistory();
    } else {
        showClientCanvas();
        hideDebugInfo();
    }
}
```

## 🔧 Implementační Fáze

### Fáze 1: Core Canvas
- [ ] SharedProjectCanvas základní třída
- [ ] CanvasSection management
- [ ] Verzování a persistence
- [ ] Basic API

### Fáze 2: Activity Tracking
- [ ] AgentActivityTracker implementace
- [ ] Real-time event system
- [ ] Agent integration pattern

### Fáze 3: Frontend Integration
- [ ] Debug/Production toggle
- [ ] Real-time activity UI
- [ ] WebSocket komunikace

### Fáze 4: Advanced Features
- [ ] Rollback functionality
- [ ] Collaborative editing
- [ ] Performance optimizations

## 🎯 Očekávané Výhody

1. **Transparentnost**: Klient vidí přesně, co se děje
2. **Debugovatelnost**: Vývojář vidí kompletní interní stav
3. **Kolaborace**: Agenti skutečně spolupracují na jednom dokumentu
4. **Verzování**: Možnost vrácení k předchozím stavům
5. **UX**: Podobné známému ChatGPT reasoning módu
6. **Škálovatelnost**: Snadno rozšiřitelné o nové agenty a sekce
