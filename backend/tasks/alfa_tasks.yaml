# Definice Úkolů pro "Výzkumnou Jednotku Alfa"

task_analyze_and_strategize:
  description: >
    <PERSON><PERSON><PERSON><PERSON> z<PERSON>r<PERSON> od klienta: "{user_input}"

    Použij knowledge_base_search pro získání relevantních informací o průzkumech.

    Na základě analýzy vygeneruj JSON s action a brief_state.
    Pokud je zpráva vágní, action = "GATHER_INFO".
    Pokud máme z<PERSON>ladní informace, action = "SUGGEST_AND_CONFIRM".
  expected_output: >
    Validní JSON objekt:
    {{
      "action": "GATHER_INFO",
      "payload": {{
        "suggestion_text": "Abych vám mohl pomoci s návrhem průzkumu, potřebuji znát více detailů...",
        "suggestion_short": "Potřebuji více informací"
      }},
      "brief_state": {{
        "project_goal": "Neurčeno",
        "target_audience": "Neurčeno",
        "survey_topics": [],
        "demographics": [],
        "localization": "Neurčeno",
        "timeline": "Neurčeno",
        "technical_output": "Neurčeno",
        "completion_percentage": 0
      }}
    }}
  agent: research_strategist_v1
  async_execution: false

task_generate_client_response:
  description: >
    Vezmi strategický JSON výstup z předchozího úkolu: {strategist_output}

    Tento JSON obsahuje:
    - action: typ akce (SUGGEST_AND_CONFIRM, CLARIFY, atd.)
    - payload: konkrétní doporučení a návrhy
    - brief_state: aktuální stav projektu

    Tvým úkolem je vytvořit profesionální, strukturovanou odpověď podle tvé
    povinné struktury (## 📋 Shrnutí Pochopení, ## 💡 Profesionální Doporučení, atd.)

    KRITICKÉ: Použij PŘESNĚ strukturu definovanou v tvém backstory!
    DŮLEŽITÉ: Vrať pouze čistý text odpovědi, žádné "Thought:" nebo "Action:" prefixy!
  expected_output: >
    Profesionální odpověď ve formátu:
    ## 📋 Shrnutí Pochopení
    ## 💡 Profesionální Doporučení
    ## 🎯 Navržený Postup
    ## ❓ Potřebuji Vaše Potvrzení
  agent: communication_expert_v1
  context:
    - task_analyze_and_strategize # Tento úkol potřebuje výstup z předchozího
  async_execution: false

task_update_project_canvas:
  description: >
    Vezmi strategický JSON výstup z předchozího úkolu: {strategist_output}

    Tento JSON obsahuje klíčovou sekci `brief_state` s těmito údaji:
    - project_goal: cíl projektu
    - target_audience: cílová skupina
    - survey_topics: témata průzkumu
    - demographics: demografické údaje
    - localization: geografické vymezení
    - timeline: časový rámec
    - technical_output: technické specifikace

    Tvým úkolem je vytvořit KOMPLETNÍ profesionální projektový dokument podle
    POVINNÉ STRUKTURY definované v tvém backstory. Musí obsahovat všechny sekce:
    - Cíl a Rozsah Projektu
    - Tematické Oblasti Průzkumu
    - Metodologie a Struktura
    - Časový Harmonogram
    - Technické Specifikace
    - Očekávané Výstupy

    KRITICKÉ: Generuj KOMPLETNÍ dokument, ne jen fragmenty!
    DŮLEŽITÉ: Vrať pouze čistý Markdown text, žádné "Thought:" nebo "Action:" prefixy!
  expected_output: >
    Kompletní profesionální projektový dokument ve formátu Markdown podle povinné
    struktury. Minimálně 50 řádků detailního obsahu s všemi sekcemi.
  agent: data_architect_v1
  context:
    - task_analyze_and_strategize # Tento úkol potřebuje výstup z předchozího
  async_execution: false # Musí čekat na dokončení prvního úkolu

master_task_create_survey_proposal:
  description: >
    **Hlavní řídící úkol:** Orchestruj kompletní proces od přijetí počátečního požadavku
    až po dodání finálního návrhu průzkumu, včetně .lss souboru. Dynamicky spouštěj
    úkoly `task_analyze_and_strategize`, `task_generate_client_response` a `task_update_project_canvas`
    v opakujícím se cyklu, dokud není dosaženo stavu `COMPLETE`.
  expected_output: >
    Kompletní balíček pro klienta:
    1. Průvodní zpráva s popisem a doporučeními.
    2. Finální podoba "Projektového Plátna" v Markdown.
    3. Příloha se souborem `pruzkum_p21.lss` připraveným k importu.
  # Tento úkol není přiřazen agentovi, ale je spravován samotnou posádkou (crew).