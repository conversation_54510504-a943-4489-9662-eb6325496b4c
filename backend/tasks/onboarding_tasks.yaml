# Konfigurace <PERSON> (Tasks) pro systém Matylda v1.0
# Ka<PERSON><PERSON><PERSON> úkol definuje specifickou činn<PERSON>, kterou má vykonat přidělený agent.
# Úkoly jsou navrženy tak, aby byly co nejvíce znovupoužitelné a řízené kontextem.

# --- ZÁKLADNÍ ÚKOLY PRO ONBOARDING ---

onboarding_master_task:
  description: >
    Tvým jediným úkolem je vést kompletní, empatický a strategický dialog s klientem,
    který začíná jeho požadavkem: '{initial_request}'.
    Cílem je získat co nejkvalitnější zadání. Postupuj v logických krocích:
    
    1. **Porozumění Cíli:** Nejprve se zaměř na 'projekt_cil' a 'klicove_rozhodnuti'. <PERSON><PERSON><PERSON> asertivní, ale empatický. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, proč je důlež<PERSON> z<PERSON> rozhodnutí, ke kterému data povedou.
    
    2. **Specifikace Obsahu:** Poté se ptej na detaily jako 'cilova_skupina' a témata průzkumu. Pokud klient neví, PROAKTIVNĚ mu nabídni možnosti na základě svých znalostí a nástrojů.
    
    3. **Technické Detaily:** Nakonec se zeptej na 'pozadovane_analyzy'.

    **KLÍČOVÉ PRAVIDLO - PŘEVZETÍ INICIATIVY:**
    Pokud klient opakovaně (2x a vícekrát) vyjádří, že něčemu nerozumí nebo neví, jak odpovědět (např. "nejsem odborník", "nevím"), ZMĚŇ STRATEGII!
    - Přestaň se ptát na tuto konkrétní informaci.
    - Jasně řekni: "Rozumím, to je naprosto v pořádku. Děkuji za upřímnost. V tom případě dovolte, abych na základě našich zkušeností navrhl nejlepší postup já."
    - Sám navrhni řešení pro daný bod (např. "Pro váš cíl bych doporučil použít standardní sadu analytických metod, včetně srovnávací analýzy a matice problémů. Souhlasíte, že toto prozatím stačí?").
    - Po jeho souhlasu si interně označ cíl za splněný (s poznámkou, že byl navržen tebou) a POKRAČUJ K DALŠÍMU BODU.

    **UKONČENÍ KONVERZACE:**
    Jakmile máš všechny klíčové informace (nebo jsi je sám navrhl a klient je odsouhlasil), je tvou povinností konverzaci elegantně ukončit.
    - Nepokládej další otázky.
    - Poděkuj klientovi za spolupráci.
    - Vytvoř finální shrnutí (brief).
    - Explicitně navrhni další kroky (např. "Na základě tohoto zadání nyní náš tým připraví první návrh, který vám zašleme k revizi.").
    
  expected_output: >
    Finální, perfektně strukturovaný JSON brief, kterému předchází přátelské a zdvořilé poděkování a jasný návrh dalších kroků.
  
  agent: client_onboarder_v1
analyze_and_create_brief:
  description: >
    Vezmi kompletní přepis dialogu, který je výstupem z úkolu 'onboarding_interview'.
    Důkladně ho zanalyzuj. Tvým úkolem je destilovat z něj esenciální informace.
  expected_output: >
    Precizní, strukturovaný JSON dokument (brief), který obsahuje všechny klíčové
    parametry projektu (projekt_cil, klicove_rozhodnuti, cilova_skupina, 
    pozadovane_analyzy, atd.). Tento brief musí být natolik jasný, aby mohl sloužit
    jako jediný zdroj pravdy pro všechny další fáze projektu.
  agent: brief_analyst_v1
  enabled: true


# --- ÚKOLY PRO ROZŠÍŘENÉ WORKFLOW ---

design_survey_architecture:
  description: >
    Na základě finálního JSON briefu, který je výstupem úkolu 'analyze_and_create_brief',
    navrhni kompletní, metodologicky podloženou architekturu výzkumného projektu.
  expected_output: >
    Strukturovaný dokument (může být JSON nebo Markdown), který detailně popisuje:
    1.  **Výzkumný Design:** Doporučený typ výzkumu (kvantitativní/kvalitativní/smíšený) s odůvodněním.
    2.  **Struktura Dotazníku:** Návrh klíčových tematických okruhů a specifických otázek.
    3.  **Vzorkovací Plán:** Definice populace, velikost vzorku a metoda výběru.
    4.  **Plán Analýz:** Seznam analýz, které budou provedeny pro zodpovězení klíčových otázek.
  agent: survey_architect_v1
  enabled: true

perform_quality_assurance:
  description: >
    Proveď finální audit kvality všech předchozích výstupů, zejména JSON briefu
    a návrhu architektury výzkumu.
    Zaměř se na soulad s původním cílem, metodologickou správnost a interní konzistenci.
    Použij své nástroje k porovnání s best practices.
  expected_output: >
    Strukturovaný report ve formátu JSON obsahující:
    - `overall_quality_score` (číslo 0-10)
    - `identified_risks` (seznam potenciálních problémů)
    - `actionable_recommendations` (seznam konkrétních doporučení pro vylepšení)
    - `final_approval` (boolean: true, pokud je výstup připraven pro klienta, jinak false)
  agent: quality_supervisor_v1
  enabled: true

learn_from_project_outcome:
  description: >
    Proveď "post-mortem" analýzu celého dokončeného projektu na základě kompletní 
    historie úkolů. Identifikuj nejúspěšnější strategii nebo postup, který vedl
    k vysoce kvalitnímu výsledku.
  expected_output: >
    Strukturovaný JSON objekt připravený pro uložení pomocí nástroje `save_best_practice`.
    Musí obsahovat klíče: `context_description`, `successful_strategy`, 
    `associated_agent_role` a `feedback_notes`.
  agent: quality_supervisor_v1
  enabled: true