# Konfigurace úkolů pro Autonomní Projektový Management
# Jeden univerzální úkol pro dynamické řešení komplexních požadavků

# Zachování kompatibility se starým systémem
task_decide_next_step:
  description: >
    **AUTONOMNÍ REŽIM:** V<PERSON><PERSON><PERSON>š komplexní požadavek uživatele dynamicky.
    Analyzuj historii chatu: {chat_history} a aktuální stav briefu: {brief_state}.

    Místo fragmentovaného přístupu převezmi iniciativu a vyřeš celý problém.
    Používej dostupné nástroje pro research a best practices.

  expected_output: >
    JSON objekt s kompletním řešením nebo dalším strategickým krokem.

  agent: strategic_onboarder_v1

task_format_canvas_output:
  description: >
    Vezmi aktuální JSON stavu briefu: {brief_state} a převe<PERSON> ho do přehledného,
    profesion<PERSON><PERSON><PERSON> vypadajícího Markdown dokumentu.

  expected_output: >
    Textový řetězec ve formátu Mark<PERSON>, který vizualizuje stav projektu.

  agent: canvas_architect_v1

task_format_chat_response:
  description: >
    Vezmi logický pokyn: {instruction_from_strategist} a celou historii chatu: {chat_history}.
    Zformuluj přirozenou, empatickou a jasnou textovou odpověď pro klienta.

  expected_output: >
    Finální textová zpráva, která se zobrazí klientovi v chatu.

  agent: interface_agent_v1

# Nový autonomní úkol
generic_project_task:
  description: >
    **HLAVNÍ ÚKOL:** Vyřeš komplexní požadavek uživatele: '{initial_request}'

    **TVŮJ PŘÍSTUP:**
    1. **ANALYZUJ** celý požadavek a pochop "velký obraz"
    2. **NAPLÁNUJ** si dynamicky kroky potřebné k dosažení cíle
    3. **EXEKUUJ** plán - komunikuj s uživatelem, používej nástroje, hledej informace
    4. **DODEJ** kompletní, finální výstup, který plně uspokojí původní požadavek

    **KLÍČOVÉ PRINCIPY:**
    - Jsi autonomní projektový manažer, ne formulář k vyplnění
    - Převezmi iniciativu, když klient neví nebo váhá
    - Používej aktivně všechny dostupné nástroje (RAG, best practices, research)
    - Myslíš strategicky - vidíš celý projekt, ne jen aktuální krok
    - Komunikuješ přirozeně a profesionálně

  expected_output: >
    Kompletní, vysoce kvalitní výstup, který plně odpovídá původnímu požadavku uživatele.

  agent: autonomous_project_manager
