#!/usr/bin/env python3
"""
MatyldaOrchestrator - Univerzální orchestrátor pro Matylda platformu
Dynamicky sestavuje týmy na základě typu projektu a domény
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional, List, Type
from pathlib import Path
from dotenv import load_dotenv
from pydantic import BaseModel, Field

# Import orchestrátorů
from specialist_orchestrator import SpecialistOrchestrator, BriefState, OrchestrationResult
from autonomous_orchestrator import AutonomousOrchestrator
from alfa_orchestrator import AlfaOrchestrator

# Načtení konfigurace
load_dotenv()

# Logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DomainConfig(BaseModel):
    """Model pro konfiguraci domény"""
    name: str = Field(..., description="Název domény")
    description: str = Field(..., description="Popis domény")
    orchestrator_type: str = Field(..., description="Typ orchestrátoru")
    agents_config: str = Field(..., description="Soubor s agenty")
    tasks_config: str = Field(..., description="Soubor s úkoly")
    crews_config: str = Field(..., description="Soubor s posádkami")
    knowledge_bases: List[Dict[str, str]] = Field(default_factory=list)
    best_practices_context: str = Field(..., description="Kontext pro best practices")
    brief_structure: List[str] = Field(default_factory=list)
    api_prefix: str = Field(..., description="API prefix")
    enabled: bool = Field(True, description="Zda je doména aktivní")

class MatyldaOrchestrator:
    """
    Univerzální orchestrátor pro Matylda platformu
    Dynamicky sestavuje týmy na základě typu projektu a domény
    """
    
    def __init__(self):
        """Inicializace univerzálního orchestrátoru"""
        self.config_dir = Path(__file__).parent / "config"
        self.domains_config: Dict[str, DomainConfig] = {}
        self.orchestrators: Dict[str, Any] = {}
        self.request_type_mapping: Dict[str, str] = {}
        self.global_config: Dict[str, Any] = {}
        
        self._load_domains_config()
        self._initialize_orchestrators()
        
        logger.info("✅ MatyldaOrchestrator inicializován")
    
    def _load_domains_config(self):
        """Načte konfiguraci domén ze YAML souboru"""
        try:
            domains_file = self.config_dir / "domains.yaml"
            
            if not domains_file.exists():
                logger.error(f"❌ Soubor {domains_file} neexistuje")
                raise FileNotFoundError(f"Domains config file not found: {domains_file}")
            
            with open(domains_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            # Načtení domén
            for domain_id, domain_data in config.get("domains", {}).items():
                self.domains_config[domain_id] = DomainConfig(**domain_data)
                logger.info(f"📚 Načtena doména: {domain_id}")
            
            # Načtení mapování request typů
            self.request_type_mapping = config.get("request_type_mapping", {})
            
            # Načtení globální konfigurace
            self.global_config = config.get("global_config", {})
            
            logger.info(f"✅ Načteno {len(self.domains_config)} domén")
            
        except Exception as e:
            logger.error(f"❌ Chyba při načítání konfigurace domén: {e}")
            raise
    
    def _initialize_orchestrators(self):
        """Inicializuje orchestrátory pro aktivní domény"""
        for domain_id, domain_config in self.domains_config.items():
            if not domain_config.enabled:
                logger.info(f"⏸️ Doména {domain_id} je deaktivována")
                continue
            
            try:
                orchestrator = self._create_orchestrator(domain_config)
                self.orchestrators[domain_id] = orchestrator
                logger.info(f"✅ Orchestrátor pro {domain_id} inicializován")
                
            except Exception as e:
                logger.error(f"❌ Chyba při inicializaci orchestrátoru pro {domain_id}: {e}")
                # Pokračujeme s dalšími doménami
    
    def _create_orchestrator(self, domain_config: DomainConfig):
        """Vytvoří orchestrátor podle typu"""
        orchestrator_type = domain_config.orchestrator_type

        if orchestrator_type == "AutonomousOrchestrator":
            return AutonomousOrchestrator()
        elif orchestrator_type == "SpecialistOrchestrator":
            return SpecialistOrchestrator()
        elif orchestrator_type == "AlfaOrchestrator":
            return AlfaOrchestrator()
        elif orchestrator_type == "SalesOrchestrator":
            # Budoucí implementace
            logger.warning(f"⚠️ SalesOrchestrator není implementován, používám AutonomousOrchestrator")
            return AutonomousOrchestrator()
        elif orchestrator_type == "ParticipationOrchestrator":
            # Budoucí implementace
            logger.warning(f"⚠️ ParticipationOrchestrator není implementován, používám AutonomousOrchestrator")
            return AutonomousOrchestrator()
        else:
            logger.warning(f"⚠️ Neznámý typ orchestrátoru {orchestrator_type}, používám AutonomousOrchestrator")
            return AutonomousOrchestrator()
    
    def determine_domain(self, initial_request: str, request_type: Optional[str] = None) -> str:
        """
        Určí doménu na základě počátečního požadavku nebo typu

        Args:
            initial_request: Počáteční požadavek od uživatele
            request_type: Explicitní typ požadavku (volitelné)

        Returns:
            ID domény
        """
        # Prozatím vždy používáme výchozí doménu (Alfa výzkumná jednotka)
        # V budoucnu bude nahrazeno AI rozpoznáním domény
        default_domain = os.getenv("DEFAULT_DOMAIN") or self.global_config.get("default_domain", "research_public_opinion_municipal")
        logger.info(f"🎯 Použita výchozí doména: {default_domain}")
        return default_domain
    
    def process_request(self, initial_request: str, request_type: Optional[str] = None,
                       session_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Zpracuje požadavek pomocí příslušné domény
        
        Args:
            initial_request: Počáteční požadavek
            request_type: Typ požadavku (volitelné)
            session_id: ID session (volitelné)
            
        Returns:
            Výsledek zpracování
        """
        try:
            # Určení domény
            domain_id = self.determine_domain(initial_request, request_type)
            
            # Kontrola dostupnosti domény
            if domain_id not in self.orchestrators:
                logger.warning(f"⚠️ Doména {domain_id} není dostupná, používám fallback")
                domain_id = os.getenv("DEFAULT_DOMAIN") or self.global_config.get("default_domain", "research_public_opinion_municipal")
            
            if domain_id not in self.orchestrators:
                raise Exception(f"Žádná dostupná doména pro zpracování požadavku")
            
            # Získání orchestrátoru
            orchestrator = self.orchestrators[domain_id]
            domain_config = self.domains_config[domain_id]
            
            logger.info(f"🎭 Zpracování požadavku v doméně: {domain_config.name}")
            
            # Vytvoření brief state podle struktury domény
            brief_state = self._create_brief_state(domain_config)
            
            # Zpracování pomocí doménového orchestrátoru
            if hasattr(orchestrator, 'process_message'):
                # SpecialistOrchestrator interface
                result = orchestrator.process_message(
                    user_message=initial_request,
                    chat_history=[],
                    brief_state=brief_state,
                    session_id=session_id or "new-session"
                )
                
                return {
                    "domain": domain_id,
                    "domain_name": domain_config.name,
                    "chat_response": result.chat_response,
                    "canvas_content": result.canvas_content,
                    "is_complete": result.is_complete,
                    "completion_percentage": result.completion_percentage,
                    "session_id": result.session_id,
                    "brief_state": result.brief_state.model_dump()
                }
            else:
                raise Exception(f"Orchestrátor pro {domain_id} nemá podporované rozhraní")
                
        except Exception as e:
            logger.error(f"❌ Chyba při zpracování požadavku: {e}")
            return {
                "error": str(e),
                "domain": "error",
                "chat_response": f"Omlouvám se, došlo k chybě při zpracování vašeho požadavku: {str(e)}",
                "canvas_content": "# ❌ Chyba\n\nDošlo k chybě při zpracování požadavku.",
                "is_complete": False,
                "completion_percentage": 0
            }
    
    def _create_brief_state(self, domain_config: DomainConfig) -> BriefState:
        """Vytvoří brief state podle struktury domény"""
        # Pro nyní používáme standardní BriefState
        # V budoucnu můžeme mít různé brief struktury pro různé domény
        return BriefState()
    
    def continue_session(self, session_id: str, user_message: str, 
                        chat_history: List[Dict[str, str]], 
                        brief_state_data: Dict[str, Any],
                        domain_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Pokračuje v existující session
        
        Args:
            session_id: ID session
            user_message: Zpráva od uživatele
            chat_history: Historie chatu
            brief_state_data: Data brief state
            domain_id: ID domény (volitelné)
            
        Returns:
            Výsledek zpracování
        """
        try:
            # Určení domény (z parametru nebo z historie)
            if not domain_id:
                domain_id = self._extract_domain_from_history(chat_history)
            
            if domain_id not in self.orchestrators:
                domain_id = os.getenv("DEFAULT_DOMAIN") or self.global_config.get("default_domain", "research_public_opinion_municipal")
            
            # Získání orchestrátoru
            orchestrator = self.orchestrators[domain_id]
            domain_config = self.domains_config[domain_id]
            
            # Rekonstrukce brief state
            brief_state = BriefState(**brief_state_data)
            
            # Zpracování zprávy
            result = orchestrator.process_message(
                user_message=user_message,
                chat_history=chat_history,
                brief_state=brief_state,
                session_id=session_id
            )
            
            return {
                "domain": domain_id,
                "domain_name": domain_config.name,
                "chat_response": result.chat_response,
                "canvas_content": result.canvas_content,
                "is_complete": result.is_complete,
                "completion_percentage": result.completion_percentage,
                "session_id": result.session_id,
                "brief_state": result.brief_state.model_dump()
            }
            
        except Exception as e:
            logger.error(f"❌ Chyba při pokračování session: {e}")
            return {
                "error": str(e),
                "domain": domain_id or "error",
                "chat_response": f"Omlouvám se, došlo k chybě: {str(e)}",
                "canvas_content": "# ❌ Chyba\n\nDošlo k chybě při zpracování.",
                "is_complete": False,
                "completion_percentage": 0
            }
    
    def _extract_domain_from_history(self, chat_history: List[Dict[str, str]]) -> str:
        """Extrahuje doménu z historie chatu"""
        # Jednoduchá implementace - v budoucnu můžeme uložit doménu do session
        return os.getenv("DEFAULT_DOMAIN") or self.global_config.get("default_domain", "research_public_opinion_municipal")
    
    def get_available_domains(self) -> List[Dict[str, Any]]:
        """Vrátí seznam dostupných domén"""
        domains = []
        for domain_id, domain_config in self.domains_config.items():
            domains.append({
                "id": domain_id,
                "name": domain_config.name,
                "description": domain_config.description,
                "enabled": domain_config.enabled,
                "available": domain_id in self.orchestrators
            })
        return domains
    
    def get_domain_info(self, domain_id: str) -> Optional[Dict[str, Any]]:
        """Vrátí informace o konkrétní doméně"""
        if domain_id not in self.domains_config:
            return None
        
        domain_config = self.domains_config[domain_id]
        return {
            "id": domain_id,
            "name": domain_config.name,
            "description": domain_config.description,
            "orchestrator_type": domain_config.orchestrator_type,
            "brief_structure": domain_config.brief_structure,
            "api_prefix": domain_config.api_prefix,
            "enabled": domain_config.enabled,
            "available": domain_id in self.orchestrators
        }

# Globální instance
matylda_orchestrator = MatyldaOrchestrator()

def get_matylda_orchestrator() -> MatyldaOrchestrator:
    """Vrátí globální instanci MatyldaOrchestrator"""
    return matylda_orchestrator

if __name__ == "__main__":
    # Test MatyldaOrchestrator
    print("🧪 Testování MatyldaOrchestrator...")
    
    orchestrator = MatyldaOrchestrator()
    
    # Test dostupných domén
    domains = orchestrator.get_available_domains()
    print(f"📊 Dostupné domény: {len(domains)}")
    for domain in domains:
        print(f"  - {domain['name']}: {'✅' if domain['available'] else '❌'}")
    
    # Test zpracování požadavku
    result = orchestrator.process_request("Potřebuji udělat průzkum spokojenosti občanů.")
    print(f"\n🎯 Doména: {result.get('domain_name', 'N/A')}")
    print(f"💬 Odpověď: {result.get('chat_response', 'N/A')[:100]}...")
    print(f"📊 Canvas: {len(result.get('canvas_content', ''))} znaků")
