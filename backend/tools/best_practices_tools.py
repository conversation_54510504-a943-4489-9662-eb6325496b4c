#!/usr/bin/env python3
"""
Nástroje pro best practices podle specifikace z tasks/best_practices.md
Implementuje sémantické vyhledávání a ukládání zkušeností pomocí Supabase a OpenAI embeddings
"""

import os
import logging
from typing import List, Dict, Any, Optional
from crewai.tools import BaseTool
from pydantic import BaseModel, Field
from openai import OpenAI
from supabase import create_client, Client
from dotenv import load_dotenv

# Načtení konfigurace
load_dotenv()

# Logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SaveBestPracticeInput(BaseModel):
    """Input schema pro uložení best practice"""
    context_description: str = Field(..., description="Popis situace/kontextu")
    successful_strategy: str = Field(..., description="Popis úspěšné strategie")
    associated_agent_role: str = Field(..., description="Role agenta (např. ClientOnboarder)")
    feedback_notes: List[str] = Field(default_factory=list, description="Dodatečné poznámky")
    success_rating: float = Field(default=0.9, description="Hodnocení úspěšnosti (0-1)")

class FindBestPracticeInput(BaseModel):
    """Input schema pro vyhledání best practices"""
    current_situation_description: str = Field(..., description="Popis současné situace")
    match_count: int = Field(default=2, description="Počet výsledků")
    match_threshold: float = Field(default=0.75, description="Práh podobnosti")

class BestPracticesManager:
    """Správce best practices s Supabase a OpenAI integrací"""
    
    def __init__(self):
        self.supabase_client = self._initialize_supabase()
        self.openai_client = self._initialize_openai()
    
    def _initialize_supabase(self) -> Optional[Client]:
        """Inicializuje Supabase klienta"""
        try:
            url = os.getenv('SUPABASE_URL')
            key = os.getenv('SUPABASE_KEY')
            
            if not url or not key:
                logger.warning("⚠️ Supabase credentials nejsou nastavené")
                return None
            
            return create_client(url, key)
        except Exception as e:
            logger.error(f"❌ Chyba při inicializaci Supabase: {e}")
            return None
    
    def _initialize_openai(self) -> Optional[OpenAI]:
        """Inicializuje OpenAI klienta"""
        try:
            api_key = os.getenv('OPENAI_API_KEY')
            if not api_key:
                logger.warning("⚠️ OpenAI API key není nastavený")
                return None
            
            return OpenAI(api_key=api_key)
        except Exception as e:
            logger.error(f"❌ Chyba při inicializaci OpenAI: {e}")
            return None
    
    def _create_embedding(self, text: str) -> Optional[List[float]]:
        """Vytvoří embedding pro text pomocí OpenAI"""
        if not self.openai_client:
            return None
        
        try:
            response = self.openai_client.embeddings.create(
                model="text-embedding-ada-002",
                input=text
            )
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"❌ Chyba při vytváření embeddingu: {e}")
            return None
    
    def save_best_practice(self, context_description: str, successful_strategy: str,
                          associated_agent_role: str, feedback_notes: List[str],
                          success_rating: float = 0.9) -> str:
        """Uloží best practice do Supabase"""
        
        if not self.supabase_client:
            return "❌ Supabase není dostupné - best practice nebyla uložena"
        
        try:
            # Vytvoření embeddingu pro kontext
            embedding = self._create_embedding(context_description)
            if not embedding:
                logger.warning("⚠️ Nepodařilo se vytvořit embedding, ukládám bez něj")
            
            # Příprava dat
            data = {
                'context_description': context_description,
                'successful_strategy': successful_strategy,
                'associated_agent_role': associated_agent_role,
                'feedback_notes': feedback_notes,
                'success_rating': success_rating,
                'embedding': embedding
            }
            
            # Uložení do Supabase
            result = self.supabase_client.table('best_practices').insert(data).execute()
            
            if result.data:
                practice_id = result.data[0]['id']
                logger.info(f"✅ Best practice uložena s ID: {practice_id}")
                return f"✅ Nová zkušenost úspěšně uložena s ID: {practice_id}"
            else:
                return "❌ Chyba při ukládání best practice"
                
        except Exception as e:
            logger.error(f"❌ Chyba při ukládání best practice: {e}")
            return f"❌ Chyba při ukládání: {str(e)}"
    
    def find_relevant_best_practices(self, current_situation_description: str,
                                   match_count: int = 2, match_threshold: float = 0.75) -> str:
        """Najde relevantní best practices pomocí sémantického vyhledávání"""
        
        if not self.supabase_client:
            return "❌ Supabase není dostupné - nelze vyhledávat best practices"
        
        try:
            # Vytvoření embeddingu pro dotaz
            query_embedding = self._create_embedding(current_situation_description)
            if not query_embedding:
                return "❌ Nepodařilo se vytvořit embedding pro vyhledávání"
            
            # Volání databázové funkce pro sémantické vyhledávání
            result = self.supabase_client.rpc(
                'match_best_practices',
                {
                    'query_embedding': query_embedding,
                    'match_threshold': match_threshold,
                    'match_count': match_count
                }
            ).execute()
            
            if not result.data:
                return "Nebyly nalezeny žádné relevantní zkušenosti z minulosti."
            
            # Formátování výsledků
            formatted_results = ["Nalezeny relevantní zkušenosti:\n"]
            
            for i, practice in enumerate(result.data, 1):
                similarity_percent = round(practice['similarity'] * 100, 1)
                formatted_results.append(f"ZKUŠENOST {i} (podobnost: {similarity_percent}%):")
                formatted_results.append(f"Situace: {practice['context_description']}")
                formatted_results.append(f"Doporučený postup: {practice['successful_strategy']}")
                formatted_results.append(f"Agent role: {practice['associated_agent_role']}")
                formatted_results.append("---")
            
            return "\n".join(formatted_results)
            
        except Exception as e:
            logger.error(f"❌ Chyba při vyhledávání best practices: {e}")
            return f"❌ Chyba při vyhledávání: {str(e)}"

# Globální instance manageru
best_practices_manager = BestPracticesManager()

class SaveBestPracticeTool(BaseTool):
    """Nástroj pro ukládání best practices"""
    name: str = "save_best_practice"
    description: str = """
    Uloží novou best practice do systému učení ze zkušeností.
    
    Tento nástroj umožňuje agentům ukládat úspěšné strategie a postupy
    pro budoucí využití. Použij ho když:
    - Objevíš efektivní postup, který by měl být zachován
    - Vyřešíš problém novým způsobem
    - Získáš pozitivní zpětnou vazbu od klienta
    - Chceš sdílet zkušenost s ostatními agenty
    
    Příklady použití:
    - Úspěšná komunikační strategie s klientem
    - Efektivní metodologie sběru dat
    - Řešení technických problémů
    """
    args_schema: type[BaseModel] = SaveBestPracticeInput
    
    def _run(self, context_description: str, successful_strategy: str,
             associated_agent_role: str, feedback_notes: List[str] = None,
             success_rating: float = 0.9) -> str:
        """Uloží best practice"""
        if feedback_notes is None:
            feedback_notes = []
        
        return best_practices_manager.save_best_practice(
            context_description=context_description,
            successful_strategy=successful_strategy,
            associated_agent_role=associated_agent_role,
            feedback_notes=feedback_notes,
            success_rating=success_rating
        )

class FindRelevantBestPracticeTool(BaseTool):
    """Nástroj pro vyhledávání relevantních best practices"""
    name: str = "find_relevant_best_practice"
    description: str = """
    Vyhledá relevantní best practices na základě současné situace.
    
    Tento nástroj používá sémantické vyhledávání pro nalezení podobných
    situací z minulosti a jejich úspěšných řešení. Použij ho když:
    - Čelíš nové nebo složité situaci
    - Hledáš inspiraci pro řešení problému
    - Chceš se vyhnout známým chybám
    - Potřebuješ ověřené postupy
    
    Příklady použití:
    - "Klient je nespokojen s kvalitou dat"
    - "Nízká response rate v online průzkumu"
    - "Problémy s komunikací s respondentem"
    """
    args_schema: type[BaseModel] = FindBestPracticeInput
    
    def _run(self, current_situation_description: str, match_count: int = 2,
             match_threshold: float = 0.75) -> str:
        """Vyhledá relevantní best practices"""
        return best_practices_manager.find_relevant_best_practices(
            current_situation_description=current_situation_description,
            match_count=match_count,
            match_threshold=match_threshold
        )

# Instance nástrojů pro export
save_best_practice = SaveBestPracticeTool()
find_relevant_best_practice = FindRelevantBestPracticeTool()

if __name__ == "__main__":
    # Test nástrojů
    print("🧪 Testování Best Practices nástrojů...")
    
    # Test uložení
    result = save_best_practice._run(
        context_description="Klient měl nejasné požadavky na výzkum",
        successful_strategy="Použil jsem strukturovaný dotazník pro upřesnění potřeb",
        associated_agent_role="ClientOnboarder",
        feedback_notes=["Klient ocenil systematický přístup"],
        success_rating=0.9
    )
    print("Uložení:", result)
    
    print("\n" + "="*50)
    
    # Test vyhledávání
    result = find_relevant_best_practice._run(
        current_situation_description="Klient neví přesně, co chce zkoumat",
        match_count=2,
        match_threshold=0.7
    )
    print("Vyhledávání:", result)
