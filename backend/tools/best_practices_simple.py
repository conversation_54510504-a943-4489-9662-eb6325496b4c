#!/usr/bin/env python3
"""
Best practices nástroje podle vzoru RAG systému
Používá SupabaseVectorStore stejně jako gdanalyst tabulka
"""

import os
import logging
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from langchain_openai import OpenAIEmbeddings
from langchain_community.vectorstores import SupabaseVectorStore
from supabase import create_client, Client
from dotenv import load_dotenv
import json

# Načtení konfigurace
load_dotenv()

# Logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DomainAwareBestPracticesSystem:
    """Domain-aware best practices systém s SupabaseVectorStore a domain support"""

    def __init__(self):
        self.supabase_url = os.getenv("SUPABASE_URL")
        self.supabase_key = os.getenv("SUPABASE_KEY")
        self.openai_api_key = os.getenv("OPENAI_API_KEY")

        # Konfigurace pro domain-aware best practices tabulku
        self.table_name = "best_practices"
        self.query_name = "match_best_practices"

        self.supabase_client: Optional[Client] = None
        self.embeddings: Optional[OpenAIEmbeddings] = None
        self.vector_store: Optional[SupabaseVectorStore] = None
        self.retriever = None

        self._initialize()
    
    def _initialize(self):
        """Inicializace všech komponent best practices systému podle vzoru RAG"""
        try:
            # Validace konfigurace
            if not self.supabase_url or not self.supabase_key:
                logger.warning("Supabase konfigurace není kompletní - Best practices systém nebude dostupný")
                return

            if not self.openai_api_key:
                logger.warning("OpenAI API klíč není nastaven - Best practices systém nebude dostupný")
                return

            # Inicializace Supabase klienta
            self.supabase_client = create_client(self.supabase_url, self.supabase_key)
            logger.info("✅ Supabase klient inicializován pro best practices")

            # Inicializace OpenAI Embeddings
            self.embeddings = OpenAIEmbeddings(
                openai_api_key=self.openai_api_key,
                model="text-embedding-ada-002"
            )
            logger.info("✅ OpenAI Embeddings inicializovány pro best practices")

            # Inicializace Vector Store
            self.vector_store = SupabaseVectorStore(
                client=self.supabase_client,
                embedding=self.embeddings,
                table_name=self.table_name,
                query_name=self.query_name
            )
            logger.info(f"✅ Supabase Vector Store inicializován (tabulka: {self.table_name})")

            # Vytvoření retrieveru
            self.retriever = self.vector_store.as_retriever(
                search_kwargs={"k": 3}  # Vrátí top 3 nejrelevantnější best practices
            )
            logger.info("✅ Best practices retriever připraven")

        except Exception as e:
            logger.error(f"❌ Chyba při inicializaci best practices systému: {e}")
            self.retriever = None
    
    def save_best_practice(self, context_description: str, successful_strategy: str,
                          associated_agent_role: str, feedback_notes: List[str],
                          success_rating: float = 0.9, domain: str = "default",
                          context: str = "general") -> str:
        """Uloží best practice pomocí vector store (stejně jako RAG)"""

        if not self.vector_store:
            return "❌ Best practices systém není dostupný - zkontrolujte konfiguraci"

        try:
            # Vytvoření textu pro embedding (kombinace kontextu a strategie)
            text_content = f"Kontext: {context_description}\nStrategie: {successful_strategy}\nAgent: {associated_agent_role}"

            # Metadata pro domain-aware best practice
            metadata = {
                'context_description': context_description,
                'successful_strategy': successful_strategy,
                'associated_agent_role': associated_agent_role,
                'feedback_notes': feedback_notes,
                'success_rating': success_rating,
                'domain': domain,
                'context': context,
                'type': 'best_practice'
            }

            logger.info(f"💾 Ukládám best practice pomocí vector store...")

            # Uložení pomocí vector store (automaticky vytvoří embedding)
            ids = self.vector_store.add_texts(
                texts=[text_content],
                metadatas=[metadata]
            )

            if ids and len(ids) > 0:
                practice_id = ids[0]
                logger.info(f"✅ Best practice uložena s ID: {practice_id}")
                return f"✅ Nová zkušenost úspěšně uložena s ID: {practice_id}"
            else:
                return "❌ Chyba při ukládání - žádné ID vráceno"

        except Exception as e:
            logger.error(f"❌ Chyba při ukládání best practice: {e}")
            return f"❌ Chyba při ukládání: {str(e)}"
    
    def find_relevant_best_practices(self, current_situation_description: str,
                                   match_count: int = 2, match_threshold: float = 0.75,
                                   domain: str = None) -> str:
        """Najde relevantní best practices pomocí vektorového vyhledávání (stejně jako RAG)"""

        if not self.retriever:
            return "❌ Best practices systém není dostupný - zkontrolujte konfiguraci"

        try:
            logger.info(f"🔍 Vyhledávám best practices pro: {current_situation_description} (doména: {domain})")

            # Domain-aware vyhledávání pokud je doména specifikována
            if domain and self.supabase_client:
                try:
                    # Vytvoření embeddingu pro domain-aware vyhledávání
                    query_embedding = None
                    if self.embeddings:
                        query_embedding = self.embeddings.embed_query(current_situation_description)

                    # Použití domain-aware funkce
                    result = self.supabase_client.rpc('search_best_practices_by_domain', {
                        'p_query': current_situation_description,
                        'p_domain': domain,
                        'p_limit': match_count,
                        'query_embedding': query_embedding
                    }).execute()

                    if result.data:
                        return self._format_domain_aware_results(result.data, current_situation_description)

                except Exception as e:
                    logger.warning(f"⚠️ Domain-aware vyhledávání selhalo: {e}, používám fallback")

            # Fallback na standardní vektorové vyhledávání
            documents = self.retriever.invoke(current_situation_description)

            if not documents:
                return "Nebyly nalezeny žádné relevantní zkušenosti z minulosti."

            # Formátování výsledků podle vzoru RAG systému
            formatted_results = ["Nalezeny relevantní zkušenosti:\n"]

            for i, doc in enumerate(documents[:match_count], 1):
                content = doc.page_content.strip()
                metadata = doc.metadata

                formatted_results.append(f"ZKUŠENOST {i}:")

                # Extrakce informací z metadat
                if metadata:
                    if 'context_description' in metadata:
                        formatted_results.append(f"Situace: {metadata['context_description']}")
                    if 'successful_strategy' in metadata:
                        formatted_results.append(f"Doporučený postup: {metadata['successful_strategy']}")
                    if 'associated_agent_role' in metadata:
                        formatted_results.append(f"Agent role: {metadata['associated_agent_role']}")
                    if 'success_rating' in metadata:
                        formatted_results.append(f"Hodnocení: {metadata['success_rating']}")
                else:
                    # Fallback na obsah dokumentu
                    formatted_results.append(f"Obsah: {content}")

                formatted_results.append("---")

            final_result = f"🎯 Nalezeno {len(documents)} relevantních zkušeností pro: '{current_situation_description}'\n\n"
            final_result += "\n".join(formatted_results)

            logger.info(f"✅ Nalezeno {len(documents)} best practices")
            return final_result

        except Exception as e:
            logger.error(f"❌ Chyba při vyhledávání best practices: {e}")
            return f"❌ Chyba při vyhledávání: {str(e)}"

    def _format_domain_aware_results(self, results: List[Dict], query: str) -> str:
        """Formátuje výsledky z domain-aware vyhledávání"""
        formatted_results = [f"🎯 Nalezeno {len(results)} domain-aware zkušeností pro: '{query}'\n"]

        for i, result in enumerate(results, 1):
            similarity = result.get('similarity', 0)
            relevance = result.get('relevance_score', 0)
            metadata = result.get('metadata', {})

            formatted_results.append(f"ZKUŠENOST {i} (podobnost: {similarity:.1%}, relevance: {relevance:.1%}):")
            formatted_results.append(f"Doména: {result.get('domain', 'N/A')} / {result.get('context', 'N/A')}")

            if metadata:
                if 'context_description' in metadata:
                    formatted_results.append(f"Situace: {metadata['context_description']}")
                if 'successful_strategy' in metadata:
                    formatted_results.append(f"Doporučený postup: {metadata['successful_strategy']}")
                if 'associated_agent_role' in metadata:
                    formatted_results.append(f"Agent role: {metadata['associated_agent_role']}")
                if 'success_rating' in metadata:
                    formatted_results.append(f"Hodnocení: {metadata['success_rating']}")
            else:
                formatted_results.append(f"Obsah: {result.get('content', 'N/A')}")

            formatted_results.append("---")

        return "\n".join(formatted_results)

    def is_available(self) -> bool:
        """Kontrola, zda je best practices systém dostupný"""
        return self.retriever is not None

def test_domain_aware_best_practices():
    """Test domain-aware best practices systému"""
    print("🧪 Testování Domain-Aware Best Practices systému...")

    manager = DomainAwareBestPracticesSystem()

    # Status check
    print(f"📊 Systém dostupný: {manager.is_available()}")
    
    # Test 1: Uložení domain-aware best practice
    print("\n📝 Test 1: Ukládání domain-aware best practice...")
    result = manager.save_best_practice(
        context_description="Klient měl nejasné požadavky na výzkum spokojenosti zaměstnanců v IT firmě",
        successful_strategy="Použil jsem strukturovaný dotazník pro upřesnění potřeb a cílů výzkumu, následovaný krátkým rozhovorem",
        associated_agent_role="ClientOnboarder",
        feedback_notes=["Klient ocenil systematický přístup", "Výsledek byl přesně podle očekávání", "Doporučil by nás dál"],
        success_rating=0.9,
        domain="onboarding_pruzkumy",
        context="metodologie"
    )
    print(f"Výsledek uložení: {result}")
    
    # Test 2: Domain-aware vyhledávání best practices
    print("\n🔍 Test 2: Domain-aware vyhledávání best practices...")
    result = manager.find_relevant_best_practices(
        current_situation_description="Klient neví přesně, co chce zkoumat v oblasti spokojenosti zaměstnanců",
        match_count=3,
        match_threshold=0.5,
        domain="onboarding_pruzkumy"
    )
    print(f"Výsledek vyhledávání:\n{result}")
    
    print("\n🎉 Testování dokončeno!")

# Aliasy pro kompatibilitu
SimpleBestPracticesManager = DomainAwareBestPracticesSystem
BestPracticesRAGSystem = DomainAwareBestPracticesSystem

if __name__ == "__main__":
    test_domain_aware_best_practices()
