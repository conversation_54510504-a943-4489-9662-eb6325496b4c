#!/usr/bin/env python3
"""
Conversational Agent pro Matylda - Nový přístup založený na LangChain
Nahrazuje rigidní CrewAI přístup flexibilním, inteligentním dialogem
"""

import os
import json
import logging
from typing import Dict, Any, Optional, List
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.output_parsers import JsonOutputParser
from pydantic import BaseModel, Field
from rag_system import get_rag_system

# Načtení konfigurace
load_dotenv()

# Logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ConversationState(BaseModel):
    """Model pro stav konverzace"""
    projekt_cil: Optional[str] = Field(None, description="Hlavní cíl projektu")
    klicove_rozhodnuti: Optional[str] = Field(None, description="Klíčové rozhodnutí, které má projekt podpořit")
    cilova_skupina: Optional[str] = Field(None, description="Cílová skupina průzkumu")
    pozadovane_analyzy: Optional[str] = Field(None, description="Požadované typy analýz")
    rozpocet: Optional[str] = Field(None, description="Rozpočet projektu")
    casovy_ramec: Optional[str] = Field(None, description="Časový rámec projektu")
    dalsi_poznamky: Optional[str] = Field(None, description="Další důležité poznámky")

class AgentResponse(BaseModel):
    """Model pro odpověď agenta"""
    response_to_client: str = Field(..., description="Odpověď pro klienta")
    updated_state_data: ConversationState = Field(..., description="Aktualizovaný stav konverzace")
    is_complete: bool = Field(False, description="Zda je konverzace dokončena")
    confidence_score: float = Field(0.0, description="Míra jistoty agenta (0-1)")

class ConversationalAgent:
    """
    Inteligentní konverzační agent pro Matylda
    Používá LangChain pro plnou kontrolu nad dialogem
    """
    
    def __init__(self):
        """Inicializace konverzačního agenta"""
        # Konfigurace
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.model_name = os.getenv("CONVERSATIONAL_MODEL", "gpt-4")
        self.temperature = float(os.getenv("CONVERSATIONAL_TEMPERATURE", "0.3"))
        self.max_tokens = int(os.getenv("CONVERSATIONAL_MAX_TOKENS", "2000"))
        
        # RAG systém
        self.rag_system = get_rag_system()
        
        # Inicializace LLM
        self.llm = ChatOpenAI(
            api_key=self.openai_api_key,
            model=self.model_name,
            temperature=self.temperature,
            max_tokens=self.max_tokens
        )
        
        # JSON parser
        self.json_parser = JsonOutputParser(pydantic_object=AgentResponse)
        
        # Systémový prompt
        self.system_prompt = self._create_system_prompt()
        
        # Prompt template
        self.prompt_template = ChatPromptTemplate.from_messages([
            ("system", self.system_prompt),
            MessagesPlaceholder(variable_name="chat_history"),
            ("human", "AKTUÁLNÍ STAV KONVERZACE:\n{current_state}\n\nNOVÁ ZPRÁVA KLIENTA:\n{user_message}\n\nINSTRUKCE: Analyzuj situaci a rozhodni se o dalším kroku podle svého myšlenkového procesu.")
        ])
        
        # Vytvoření řetězce
        self.chain = self.prompt_template | self.llm | self.json_parser
        
        logger.info("✅ ConversationalAgent inicializován")
    
    def _create_system_prompt(self) -> str:
        """Vytvoří systémový prompt pro agenta"""
        return """Jsi Matylda, geniální strategický konzultant. Tvojí prací je vést klienta od vágního nápadu ke konkrétnímu, akceschopnému zadání projektu.

# PRAVIDLA CHOVÁNÍ
- Vždy buď empatický, profesionální a proaktivní.
- Vždy potvrď a parafrázuj, co klient řekl, než položíš další otázku.
- Pokud klient neví, převezmi iniciativu a sám navrhni řešení.
- **Pravidlo pro cenu:** Pokud se klient zeptá na cenu, NEODPOVÍDEJ na otázku. Tvojí jedinou odpovědí musí být: "Rozumím Vašemu dotazu na cenu. Jako AI konzultant nemám přístup k ceníku. Prosím, obraťte se na naše obchodní oddělení. Můžeme se nyní vrátit k [poslední probírané téma]?"

# TVŮJ MYŠLENKOVÝ PROCES (VŽDY HO DODRŽUJ):
1. **ANALYZUJ STAV:** Podívej se na celou dosavadní historii chatu a na svůj interní "poznámkový blok" (JSON se stavem), kde vidíš, co už víš a co ti chybí.
2. **IDENTIFIKUJ DALŠÍ KROK:** Na základě analýzy se rozhodni, co je teď nejdůležitější:
   * **A) Potřebuji upřesnit informaci?** (Klientova odpověď byla příliš obecná).
   * **B) Potřebuji získat novou informaci?** (Přecházím na další bod v mém seznamu cílů).
   * **C) Mám dost informací, abych mohl něco navrhnout?** (Klient neví, já převezmu iniciativu).
   * **D) Jsou všechny cíle splněny?**
3. **ZFORMULUJ ODPOVĚĎ:** Na základě svého rozhodnutí vytvoř odpověď.
   * Vždy začni empatickým potvrzením toho, co klient řekl.
   * Pokud něco navrhuješ (krok C), jasně to řekni: "Na základě toho, co jste řekl, bych doporučil..." a použij své znalosti.
   * Pokud se ptáš, polož jednu, jasnou otázku.
   * Pokud jsou cíle splněny (krok D), poděkuj, shrň výsledky a navrhni další kroky.

# TVŮJ "POZNÁMKOVÝ BLOK" (Cíle, které musíš naplnit):
- projekt_cil: Hlavní cíl projektu
- klicove_rozhodnuti: Klíčové rozhodnutí, které má projekt podpořit
- cilova_skupina: Cílová skupina průzkumu
- pozadovane_analyzy: Požadované typy analýz
- rozpocet: Rozpočet projektu (volitelné)
- casovy_ramec: Časový rámec projektu (volitelné)

# PRAVIDLA VÝSTUPU (NEJDŮLEŽITĚJŠÍ!)
ABSOLUTNĚ ZAKÁZÁNO: Jakékoliv komentáře, vysvětlení, analýzy nebo text před nebo za JSON objektem!

Tvůj výstup MUSÍ začínat znakem {{ a končit znakem }}.
ŽÁDNÝ jiný text není povolen!

Struktura JSON:
{{
  "response_to_client": "Text, který se má zobrazit klientovi.",
  "updated_state_data": {{
    "projekt_cil": "hodnota nebo null",
    "klicove_rozhodnuti": "hodnota nebo null",
    "cilova_skupina": "hodnota nebo null",
    "pozadovane_analyzy": "hodnota nebo null",
    "rozpocet": "hodnota nebo null",
    "casovy_ramec": "hodnota nebo null",
    "dalsi_poznamky": "hodnota nebo null"
  }},
  "is_complete": false,
  "confidence_score": 0.8
}}

PŘÍKLAD ŠPATNÉ ODPOVĚDI (NIKDY NEDĚLEJ):
ANALÝZA: Klient chce...
{{
  "response_to_client": "..."
}}

PŘÍKLAD SPRÁVNÉ ODPOVĚDI:
{{
  "response_to_client": "Rozumím, že chcete...",
  "updated_state_data": {{...}},
  "is_complete": false,
  "confidence_score": 0.8
}}
Nastav is_complete na true pouze když máš všechny klíčové informace (projekt_cil, klicove_rozhodnuti) a alespoň základní představu o cilova_skupina."""
    
    def process_message(self, user_message: str, chat_history: List[Dict[str, str]], 
                       current_state: ConversationState) -> AgentResponse:
        """
        Zpracuje zprávu od uživatele a vrátí odpověď agenta
        
        Args:
            user_message: Zpráva od uživatele
            chat_history: Historie konverzace
            current_state: Aktuální stav konverzace
            
        Returns:
            Odpověď agenta s aktualizovaným stavem
        """
        try:
            # Převod historie na LangChain messages
            messages = []
            for entry in chat_history:
                if entry["type"] == "user":
                    messages.append(HumanMessage(content=entry["content"]))
                elif entry["type"] == "agent":
                    messages.append(AIMessage(content=entry["content"]))
            
            # Příprava vstupu pro řetězec
            chain_input = {
                "chat_history": messages,
                "current_state": current_state.model_dump_json(indent=2),
                "user_message": user_message
            }
            
            # Spuštění řetězce
            logger.info(f"🤖 Zpracovávám zprávu: {user_message[:100]}...")
            response = self.chain.invoke(chain_input)

            # Validace odpovědi
            if not isinstance(response, dict):
                raise ValueError("Odpověď není ve správném formátu")

            # Kontrola na error odpověď
            if "error" in response:
                logger.error(f"❌ LLM vrátil chybu: {response['error']}")
                return AgentResponse(
                    response_to_client="Omlouvám se, došlo k technické chybě. Můžete prosím zopakovat svou zprávu?",
                    updated_state_data=current_state,
                    is_complete=False,
                    confidence_score=0.0
                )

            # Vytvoření AgentResponse objektu
            agent_response = AgentResponse(**response)

            # ZDRAVÝ ROZUM: Kontrola zachování informací
            if not self._validate_state_preservation(current_state, agent_response.updated_state_data):
                logger.warning("⚠️ Detekována ztráta informací, provádím sloučení stavů...")
                agent_response.updated_state_data = self._merge_states(current_state, agent_response.updated_state_data)

                # Přidání poznámky do odpovědi
                agent_response.response_to_client += " (Poznámka: Zachoval jsem všechny dosud získané informace.)"

            logger.info(f"✅ Zpráva zpracována, is_complete: {agent_response.is_complete}")
            return agent_response
            
        except Exception as e:
            logger.error(f"❌ Chyba při zpracování zprávy: {e}")

            # Pokud je to JSON parsing error, zkusíme to znovu s jednodušším promptem
            if "Invalid json output" in str(e) or "json" in str(e).lower():
                logger.warning("🔄 Detekována JSON chyba, zkouším jednodušší přístup...")
                try:
                    # Jednoduchý fallback prompt
                    simple_prompt = f"""Odpověz pouze validním JSON objektem na tuto zprávu: "{user_message}"

Formát:
{{
  "response_to_client": "Tvá odpověď",
  "updated_state_data": {{
    "projekt_cil": "{current_state.projekt_cil or 'null'}",
    "klicove_rozhodnuti": "{current_state.klicove_rozhodnuti or 'null'}",
    "cilova_skupina": "{current_state.cilova_skupina or 'null'}",
    "pozadovane_analyzy": "{current_state.pozadovane_analyzy or 'null'}",
    "rozpocet": "{current_state.rozpocet or 'null'}",
    "casovy_ramec": "{current_state.casovy_ramec or 'null'}",
    "dalsi_poznamky": "{current_state.dalsi_poznamky or 'null'}"
  }},
  "is_complete": false,
  "confidence_score": 0.5
}}"""

                    fallback_response = self.llm.invoke(simple_prompt)
                    fallback_parsed = self.json_parser.parse(fallback_response.content)
                    return AgentResponse(**fallback_parsed)

                except Exception as fallback_error:
                    logger.error(f"❌ I fallback selhal: {fallback_error}")

            # Finální fallback odpověď
            return AgentResponse(
                response_to_client="Omlouvám se, došlo k technické chybě. Můžete prosím zopakovat svou zprávu jinak?",
                updated_state_data=current_state,
                is_complete=False,
                confidence_score=0.0
            )
    
    def _validate_state_preservation(self, old_state: ConversationState, new_state: ConversationState) -> bool:
        """
        Kontroluje, zda nový stav neztrácí informace ze starého stavu.
        Vrací True, pokud je nový stav v pořádku, False pokud ztratil informace.
        """
        # Kontrola všech polí - pokud bylo ve starém stavu něco vyplněno a v novém je None, je to problém
        old_dict = old_state.model_dump()
        new_dict = new_state.model_dump()

        lost_information = []

        for key, old_value in old_dict.items():
            new_value = new_dict.get(key)

            # Pokud byla informace ve starém stavu a v novém není (nebo je prázdná)
            if old_value and old_value.strip() and (not new_value or not new_value.strip()):
                lost_information.append(f"{key}: '{old_value}' -> '{new_value}'")

        if lost_information:
            logger.warning(f"⚠️ Detekována ztráta informací: {lost_information}")
            return False

        return True

    def _merge_states(self, old_state: ConversationState, new_state: ConversationState) -> ConversationState:
        """
        Sloučí stavy tak, aby se neztrácely informace.
        Preferuje nové hodnoty, ale zachovává staré, pokud nové jsou prázdné.
        """
        old_dict = old_state.model_dump()
        new_dict = new_state.model_dump()
        merged_dict = {}

        for key in old_dict.keys():
            old_value = old_dict.get(key)
            new_value = new_dict.get(key)

            # Pokud je nová hodnota prázdná/None, ale stará má obsah, zachovej starou
            if old_value and old_value.strip() and (not new_value or not new_value.strip()):
                merged_dict[key] = old_value
                logger.info(f"🔄 Zachovávám starou hodnotu pro {key}: '{old_value}'")
            else:
                merged_dict[key] = new_value

        return ConversationState(**merged_dict)

    def search_knowledge_base(self, query: str) -> str:
        """Vyhledá informace v znalostní bázi"""
        if self.rag_system.is_available():
            return self.rag_system.search_knowledge_base(query)
        else:
            return "Znalostní báze není dostupná."
    
    def get_status(self) -> Dict[str, Any]:
        """Vrátí status agenta"""
        return {
            "model": self.model_name,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "rag_available": self.rag_system.is_available(),
            "initialized": True
        }

# Globální instance
conversational_agent = ConversationalAgent()

def get_conversational_agent() -> ConversationalAgent:
    """Vrátí globální instanci konverzačního agenta"""
    return conversational_agent

if __name__ == "__main__":
    # Test konverzačního agenta
    print("🧪 Testování ConversationalAgent...")
    
    agent = ConversationalAgent()
    status = agent.get_status()
    print(f"📊 Status: {status}")
    
    # Test zprávy
    test_state = ConversationState()
    test_history = []
    test_message = "Potřebuji udělat průzkum spokojenosti občanů."
    
    response = agent.process_message(test_message, test_history, test_state)
    print(f"\n🤖 Odpověď agenta:")
    print(f"Klient: {response.response_to_client}")
    print(f"Stav: {response.updated_state_data}")
    print(f"Dokončeno: {response.is_complete}")
