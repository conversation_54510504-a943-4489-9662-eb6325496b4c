#!/usr/bin/env python3
"""
API Gateway pro Matylda v2.0 - Univerzální platforma
Směruje požadavky na správné mikroservisy podle domény
"""

import os
import logging
from typing import Dict, Any, Optional, List
from fastapi import FastAPI, HTTPException, Request, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from dotenv import load_dotenv
from matylda_orchestrator import get_matylda_orchestrator
from multi_domain_rag import get_multi_domain_rag
from best_practices_system import get_best_practices_system

# Načtení konfigurace
load_dotenv()

# Logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GatewayRequest(BaseModel):
    """Univerzální request model pro gateway"""
    message: str = Field(..., description="Zpráva od uživatele")
    session_id: Optional[str] = Field(None, description="ID session")
    domain: Optional[str] = Field(None, description="Explicitní doména")
    request_type: Optional[str] = Field(None, description="Typ požadavku")
    context: Optional[str] = Field(None, description="Kontext požadavku")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Dodatečná metadata")

class GatewayResponse(BaseModel):
    """Univerzální response model pro gateway"""
    success: bool = Field(..., description="Zda byl požadavek úspěšný")
    data: Optional[Dict[str, Any]] = Field(None, description="Data odpovědi")
    error: Optional[str] = Field(None, description="Chybová zpráva")
    domain: Optional[str] = Field(None, description="Použitá doména")
    service: Optional[str] = Field(None, description="Použitý mikroservis")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Metadata odpovědi")

class MatyldaAPIGateway:
    """
    API Gateway pro Matylda platformu
    Směruje požadavky na správné mikroservisy podle domény a typu
    """
    
    def __init__(self):
        """Inicializace API Gateway"""
        self.orchestrator = get_matylda_orchestrator()
        self.rag_system = get_multi_domain_rag()
        self.best_practices = get_best_practices_system()
        
        # Mapování služeb
        self.service_mapping = {
            'chat': self._handle_chat_request,
            'knowledge': self._handle_knowledge_request,
            'best_practices': self._handle_best_practices_request,
            'analytics': self._handle_analytics_request,
            'admin': self._handle_admin_request
        }
        
        logger.info("✅ MatyldaAPIGateway inicializován")
    
    def route_request(self, service: str, request: GatewayRequest) -> GatewayResponse:
        """
        Hlavní routing metoda
        
        Args:
            service: Název služby (chat, knowledge, best_practices, analytics, admin)
            request: Request data
            
        Returns:
            Gateway response
        """
        try:
            logger.info(f"🔀 Routing požadavku na službu: {service}")
            
            # Kontrola existence služby
            if service not in self.service_mapping:
                return GatewayResponse(
                    success=False,
                    error=f"Neznámá služba: {service}",
                    service=service
                )
            
            # Určení domény pokud není explicitně zadána
            if not request.domain and service == 'chat':
                request.domain = self.orchestrator.determine_domain(
                    request.message, 
                    request.request_type
                )
            
            # Volání příslušné služby
            handler = self.service_mapping[service]
            result = handler(request)
            
            return GatewayResponse(
                success=True,
                data=result,
                domain=request.domain,
                service=service,
                metadata={"routing_time": "< 100ms"}  # Placeholder
            )
            
        except Exception as e:
            logger.error(f"❌ Chyba při routing: {e}")
            return GatewayResponse(
                success=False,
                error=str(e),
                service=service
            )
    
    def _handle_chat_request(self, request: GatewayRequest) -> Dict[str, Any]:
        """Zpracuje chat požadavek"""
        try:
            if not request.session_id:
                # Nová konverzace
                result = self.orchestrator.process_request(
                    initial_request=request.message,
                    request_type=request.request_type
                )
            else:
                # Pokračování konverzace - zatím fallback
                result = self.orchestrator.process_request(
                    initial_request=request.message,
                    request_type=request.request_type
                )
            
            return {
                "chat_response": result.get("chat_response"),
                "canvas_content": result.get("canvas_content"),
                "session_id": result.get("session_id"),
                "is_complete": result.get("is_complete", False),
                "completion_percentage": result.get("completion_percentage", 0),
                "domain_name": result.get("domain_name")
            }
            
        except Exception as e:
            logger.error(f"❌ Chyba při zpracování chat požadavku: {e}")
            raise
    
    def _handle_knowledge_request(self, request: GatewayRequest) -> Dict[str, Any]:
        """Zpracuje knowledge base požadavek"""
        try:
            # Vyhledávání v znalostní bázi
            results = self.rag_system.search_knowledge_base(
                query=request.message,
                domain=request.domain,
                k=request.metadata.get("limit", 5)
            )
            
            return {
                "results": results,
                "query": request.message,
                "domain": request.domain,
                "knowledge_bases": self.rag_system.get_available_knowledge_bases(request.domain)
            }
            
        except Exception as e:
            logger.error(f"❌ Chyba při zpracování knowledge požadavku: {e}")
            raise
    
    def _handle_best_practices_request(self, request: GatewayRequest) -> Dict[str, Any]:
        """Zpracuje best practices požadavek"""
        try:
            # Vyhledávání best practices
            practices = self.best_practices.search_best_practices(
                query=request.message,
                domain=request.domain,
                context=request.context,
                category=request.metadata.get("category"),
                limit=request.metadata.get("limit", 5)
            )
            
            # Konverze na dict pro JSON serialization
            practices_data = []
            for practice in practices:
                practices_data.append({
                    "id": practice.id,
                    "title": practice.title,
                    "category": practice.category,
                    "description": practice.description,
                    "domain": practice.domain,
                    "effectiveness_score": practice.effectiveness_score,
                    "usage_count": practice.usage_count
                })
            
            return {
                "practices": practices_data,
                "query": request.message,
                "domain": request.domain,
                "context": request.context,
                "total_found": len(practices_data)
            }
            
        except Exception as e:
            logger.error(f"❌ Chyba při zpracování best practices požadavku: {e}")
            raise
    
    def _handle_analytics_request(self, request: GatewayRequest) -> Dict[str, Any]:
        """Zpracuje analytics požadavek"""
        try:
            # Domain statistiky
            domain_stats = self.best_practices.get_domain_statistics(request.domain)
            
            # RAG status
            rag_status = self.rag_system.get_status()
            
            # Orchestrator domény
            available_domains = self.orchestrator.get_available_domains()
            
            return {
                "domain_statistics": domain_stats,
                "rag_status": rag_status,
                "available_domains": available_domains,
                "requested_domain": request.domain
            }
            
        except Exception as e:
            logger.error(f"❌ Chyba při zpracování analytics požadavku: {e}")
            raise
    
    def _handle_admin_request(self, request: GatewayRequest) -> Dict[str, Any]:
        """Zpracuje admin požadavek"""
        try:
            action = request.metadata.get("action", "status")
            
            if action == "status":
                return {
                    "gateway_status": "healthy",
                    "services": {
                        "orchestrator": "available",
                        "rag_system": "available" if self.rag_system.is_available() else "unavailable",
                        "best_practices": "available"
                    },
                    "domains": self.orchestrator.get_available_domains()
                }
            elif action == "reload_config":
                # Reload konfigurace - placeholder
                return {"message": "Konfigurace znovu načtena"}
            else:
                raise ValueError(f"Neznámá admin akce: {action}")
                
        except Exception as e:
            logger.error(f"❌ Chyba při zpracování admin požadavku: {e}")
            raise
    
    def get_service_health(self) -> Dict[str, Any]:
        """Vrátí health status všech služeb"""
        return {
            "gateway": "healthy",
            "orchestrator": "healthy",
            "rag_system": "healthy" if self.rag_system.is_available() else "unhealthy",
            "best_practices": "healthy",
            "domains_count": len(self.orchestrator.get_available_domains()),
            "active_domains": len([d for d in self.orchestrator.get_available_domains() if d["available"]])
        }

# Globální instance
api_gateway = MatyldaAPIGateway()

def get_api_gateway() -> MatyldaAPIGateway:
    """Vrátí globální instanci API gateway"""
    return api_gateway

# FastAPI aplikace pro gateway
gateway_app = FastAPI(
    title="Matylda API Gateway",
    description="Univerzální API gateway pro Matylda platformu",
    version="2.0.0"
)

# CORS middleware
gateway_app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@gateway_app.post("/gateway/{service}", response_model=GatewayResponse)
async def gateway_endpoint(service: str, request: GatewayRequest):
    """Hlavní gateway endpoint"""
    gateway = get_api_gateway()
    return gateway.route_request(service, request)

@gateway_app.get("/gateway/health")
async def gateway_health():
    """Health check pro gateway"""
    gateway = get_api_gateway()
    return gateway.get_service_health()

@gateway_app.get("/gateway/services")
async def list_services():
    """Seznam dostupných služeb"""
    return {
        "services": [
            {
                "name": "chat",
                "description": "Konverzační služba s AI agenty",
                "endpoint": "/gateway/chat"
            },
            {
                "name": "knowledge",
                "description": "Vyhledávání ve znalostních bázích",
                "endpoint": "/gateway/knowledge"
            },
            {
                "name": "best_practices",
                "description": "Vyhledávání best practices",
                "endpoint": "/gateway/best_practices"
            },
            {
                "name": "analytics",
                "description": "Analytické služby a statistiky",
                "endpoint": "/gateway/analytics"
            },
            {
                "name": "admin",
                "description": "Administrační funkce",
                "endpoint": "/gateway/admin"
            }
        ]
    }

if __name__ == "__main__":
    # Test API Gateway
    print("🧪 Testování API Gateway...")
    
    gateway = MatyldaAPIGateway()
    
    # Test chat služby
    test_request = GatewayRequest(
        message="Potřebuji udělat průzkum spokojenosti občanů",
        request_type="průzkum"
    )
    
    result = gateway.route_request("chat", test_request)
    print(f"✅ Chat služba: {result.success}")
    print(f"🎯 Doména: {result.domain}")
    
    # Test health
    health = gateway.get_service_health()
    print(f"🏥 Health status: {health}")
    
    print("\n✅ API Gateway připraven!")
