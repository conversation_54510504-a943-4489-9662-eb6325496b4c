#!/usr/bin/env python3
"""
Test nové best_practices tabulky a funkce se správnými parametry
"""

import os
from supabase import create_client, Client
from dotenv import load_dotenv

load_dotenv()

def test_new_best_practices():
    """Test nové best_practices tabulky a funkce"""
    
    url = os.getenv('SUPABASE_URL')
    key = os.getenv('SUPABASE_KEY')
    
    supabase: Client = create_client(url, key)
    print("✅ Připojen k Supabase")
    
    # Test 1: Čtení z nové tabulky
    print("\n📋 Test 1: Čtení z nové tabulky best_practices...")
    try:
        result = supabase.table('best_practices').select('*').execute()
        print(f"✅ Tabulka best_practices existuje, nalezeno {len(result.data)} záznamů")
        if result.data:
            record = result.data[0]
            print("📊 Struktura záznamu:")
            for key, value in record.items():
                if key == 'embedding':
                    print(f"  - {key}: vector({len(value) if value else 0})")
                elif key == 'metadata':
                    print(f"  - {key}: {type(value).__name__} = {value}")
                else:
                    print(f"  - {key}: {type(value).__name__} = {str(value)[:50]}...")
    except Exception as e:
        print(f"❌ Chyba při čtení best_practices: {e}")
    
    # Test 2: Test funkce match_best_practices se správnými parametry
    print("\n🔧 Test 2: Funkce match_best_practices (filter, match_count, query_embedding)...")
    try:
        dummy_embedding = [0.1] * 1536
        result = supabase.rpc('match_best_practices', {
            'filter': {},
            'match_count': 3,
            'query_embedding': dummy_embedding
        }).execute()
        print(f"✅ Funkce match_best_practices funguje: {len(result.data)} výsledků")
        if result.data:
            print("📊 Struktura výsledku:")
            for key, value in result.data[0].items():
                print(f"  - {key}: {type(value).__name__}")
    except Exception as e:
        print(f"❌ Funkce match_best_practices: {e}")
    
    # Test 3: Test bez filter parametru
    print("\n🔧 Test 3: Bez filter parametru...")
    try:
        dummy_embedding = [0.1] * 1536
        result = supabase.rpc('match_best_practices', {
            'match_count': 3,
            'query_embedding': dummy_embedding
        }).execute()
        print(f"✅ Bez filter funguje: {len(result.data)} výsledků")
    except Exception as e:
        print(f"❌ Bez filter: {e}")
    
    # Test 4: Test vložení nového záznamu
    print("\n📝 Test 4: Vložení nového záznamu...")
    try:
        test_data = {
            'content': 'Test best practice: Při problémech s response rate použít reminder emails',
            'metadata': {
                'context_description': 'Nízká response rate v online průzkumu',
                'successful_strategy': 'Poslání reminder emailů po 3 a 7 dnech',
                'associated_agent_role': 'SurveyArchitect',
                'success_rating': 0.85,
                'feedback_notes': ['Response rate se zvýšila o 15%'],
                'type': 'best_practice'
            }
        }
        
        result = supabase.table('best_practices').insert(test_data).execute()
        
        if result.data:
            test_id = result.data[0]['id']
            print(f"✅ Nový záznam vložen s ID: {test_id}")
            
            # Test čtení vloženého záznamu
            read_result = supabase.table('best_practices').select('*').eq('id', test_id).execute()
            if read_result.data:
                print("✅ Záznam úspěšně načten")
            
        else:
            print("❌ Vložení selhalo - žádná data")
            
    except Exception as e:
        print(f"❌ Chyba při vkládání: {e}")
    
    print("\n🎉 Test dokončen!")

if __name__ == "__main__":
    print("🧪 Test nové best_practices tabulky...")
    test_new_best_practices()
