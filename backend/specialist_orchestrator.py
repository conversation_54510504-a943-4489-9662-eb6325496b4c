#!/usr/bin/env python3
"""
Specialist Orchestrator pro Matylda - Architektura "T<PERSON><PERSON> Specialistů"
Nahrazuje ConversationalAgent orchestrátorem tří specializovaných agentů
"""

import os
import json
import logging
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime
from dotenv import load_dotenv
from crewai import Agent, Task, Crew, Process
from pydantic import BaseModel, Field
from config_loader import MatyldaConfigLoader
from multi_domain_rag import get_multi_domain_rag
from best_practices_system import get_best_practices_system

# Načtení konfigurace
load_dotenv()

# Logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BriefState(BaseModel):
    """Model pro stav projektového briefu"""
    projekt_cil: Optional[str] = Field(None, description="Hlavní cíl projektu")
    klicove_rozhodnuti: Optional[str] = Field(None, description="Klíčové rozhodnutí")
    cilova_skupina: Optional[str] = Field(None, description="Cílová skupina")
    pozadovane_analyzy: Optional[str] = Field(None, description="Požadované analýzy")
    casovy_ramec: Optional[str] = Field(None, description="Časový rámec")
    rozpocet: Optional[str] = Field(None, description="Rozpočet")
    dalsi_poznamky: Optional[str] = Field(None, description="Další poznámky")

class StrategistResponse(BaseModel):
    """Model pro odpověď strategického agenta"""
    thought_process: str = Field(..., description="Myšlenkový proces")
    next_action: str = Field(..., description="Další akce (GATHER_INFO, CLARIFY, SUGGEST, COMPLETE)")
    target_field: Optional[str] = Field(None, description="Cílové pole")
    suggested_instruction: str = Field(..., description="Pokyn pro interface agenta")
    updated_brief_state: BriefState = Field(..., description="Aktualizovaný stav briefu")
    completion_percentage: int = Field(..., description="Procento dokončení")

    @classmethod
    def from_dict(cls, data: dict):
        """Vytvoří instanci z dict s dodatečnou validací"""
        # Oprava target_field pokud je array
        if isinstance(data.get("target_field"), list):
            data["target_field"] = ", ".join(data["target_field"])

        return cls(**data)

class OrchestrationResult(BaseModel):
    """Model pro výsledek orchestrace"""
    session_id: str = Field(..., description="ID session")
    chat_response: str = Field(..., description="Odpověď pro chat")
    canvas_content: str = Field(..., description="Obsah pro canvas")
    is_complete: bool = Field(False, description="Zda je konverzace dokončena")
    brief_state: BriefState = Field(..., description="Aktuální stav briefu")
    completion_percentage: int = Field(0, description="Procento dokončení")

class SpecialistOrchestrator:
    """
    Orchestrátor specializovaných agentů pro architekturu "Týmu Specialistů"
    Řídí spolupráci strategic_onboarder_v1, interface_agent_v1 a canvas_architect_v1
    """
    
    def __init__(self):
        """Inicializace orchestrátoru"""
        self.config_loader = MatyldaConfigLoader()
        self.rag_system = get_multi_domain_rag()
        self.best_practices = get_best_practices_system()
        
        # Načtení agentů
        self.strategic_agent = None
        self.interface_agent = None
        self.canvas_agent = None
        
        # Načtení úkolů
        self.strategic_task_template = None
        self.interface_task_template = None
        self.canvas_task_template = None
        
        self._initialize_agents_and_tasks()
        
        logger.info("✅ SpecialistOrchestrator inicializován")
    
    def _initialize_agents_and_tasks(self):
        """Inicializuje agenty a úkoly ze specialist konfigurací"""
        try:
            # Načtení specialist agentů
            self.strategic_agent = self.config_loader.create_agent(
                "strategic_onboarder_v1", 
                config_file="specialist_agents.yaml"
            )
            self.interface_agent = self.config_loader.create_agent(
                "interface_agent_v1",
                config_file="specialist_agents.yaml"
            )
            self.canvas_agent = self.config_loader.create_agent(
                "canvas_architect_v1",
                config_file="specialist_agents.yaml"
            )
            
            # Načtení specialist úkolů
            self.strategic_task_template = self.config_loader.get_task_config(
                "task_decide_next_step",
                config_file="specialist_tasks.yaml"
            )
            self.interface_task_template = self.config_loader.get_task_config(
                "task_format_chat_response",
                config_file="specialist_tasks.yaml"
            )
            self.canvas_task_template = self.config_loader.get_task_config(
                "task_format_canvas_output",
                config_file="specialist_tasks.yaml"
            )
            
            if not all([self.strategic_agent, self.interface_agent, self.canvas_agent]):
                raise Exception("Nepodařilo se načíst všechny specialist agenty")
            
            if not all([self.strategic_task_template, self.interface_task_template, self.canvas_task_template]):
                raise Exception("Nepodařilo se načíst všechny specialist úkoly")
            
            logger.info("✅ Specialist agenti a úkoly načteny")
            
        except Exception as e:
            logger.error(f"❌ Chyba při inicializaci agentů a úkolů: {e}")
            raise
    
    def process_message(self, user_message: str, chat_history: List[Dict[str, str]], 
                       brief_state: BriefState, session_id: str) -> OrchestrationResult:
        """
        Zpracuje zprávu pomocí týmu specializovaných agentů
        
        Args:
            user_message: Zpráva od uživatele
            chat_history: Historie konverzace
            brief_state: Aktuální stav briefu
            session_id: ID session
            
        Returns:
            Výsledek orchestrace s odpovědí a canvas obsahem
        """
        try:
            logger.info(f"🎭 Orchestrace pro session {session_id}")
            
            # KROK 1: Strategické rozhodnutí
            strategist_result = self._run_strategist(user_message, chat_history, brief_state)
            
            # KROK 2: Paralelní zpracování interface a canvas agentů
            chat_response = self._run_interface_agent(
                strategist_result.suggested_instruction, 
                chat_history
            )
            
            canvas_content = self._run_canvas_agent(strategist_result.updated_brief_state)
            
            # KROK 3: Sestavení finálního výsledku
            is_complete = strategist_result.next_action == "COMPLETE"
            
            result = OrchestrationResult(
                session_id=session_id,
                chat_response=chat_response,
                canvas_content=canvas_content,
                is_complete=is_complete,
                brief_state=strategist_result.updated_brief_state,
                completion_percentage=strategist_result.completion_percentage
            )
            
            logger.info(f"✅ Orchestrace dokončena: {strategist_result.completion_percentage}% hotovo")
            return result
            
        except Exception as e:
            logger.error(f"❌ Chyba při orchestraci: {e}")
            # Fallback odpověď
            return OrchestrationResult(
                session_id=session_id,
                chat_response=f"Omlouvám se, došlo k chybě při zpracování. Můžete prosím zopakovat svou zprávu?",
                canvas_content=self._generate_fallback_canvas(brief_state),
                is_complete=False,
                brief_state=brief_state,
                completion_percentage=0
            )
    
    def _run_strategist(self, user_message: str, chat_history: List[Dict[str, str]], 
                       brief_state: BriefState) -> StrategistResponse:
        """Spustí strategického agenta pro rozhodnutí o dalším kroku"""
        try:
            # Příprava kontextu
            chat_history_str = self._format_chat_history(chat_history, user_message)
            brief_state_str = brief_state.model_dump_json(indent=2)
            
            # Vytvoření úkolu
            task = Task(
                description=self.strategic_task_template["description"].format(
                    chat_history=chat_history_str,
                    brief_state=brief_state_str
                ),
                expected_output=self.strategic_task_template["expected_output"],
                agent=self.strategic_agent
            )
            
            # Spuštění posádky
            crew = Crew(
                agents=[self.strategic_agent],
                tasks=[task],
                process=Process.sequential,
                verbose=False,
                memory=False
            )
            
            result = crew.kickoff()
            
            # Parsování JSON odpovědi
            try:
                result_str = str(result)

                # Pokus o přímé parsování
                try:
                    result_dict = json.loads(result_str)
                    return StrategistResponse.from_dict(result_dict)
                except json.JSONDecodeError:
                    # Pokus o extrakci JSON z markdown bloku
                    import re
                    json_match = re.search(r'```(?:json)?\s*(\{.*?\})\s*```', result_str, re.DOTALL)
                    if json_match:
                        json_str = json_match.group(1)
                        result_dict = json.loads(json_str)
                        return StrategistResponse.from_dict(result_dict)
                    else:
                        raise json.JSONDecodeError("Nepodařilo se najít JSON", result_str, 0)

            except (json.JSONDecodeError, Exception) as e:
                # Fallback parsing
                logger.warning(f"⚠️ Nepodařilo se parsovat JSON od strategického agenta: {e}")
                return self._create_fallback_strategist_response(brief_state, user_message)
                
        except Exception as e:
            logger.error(f"❌ Chyba při spuštění strategického agenta: {e}")
            return self._create_fallback_strategist_response(brief_state, user_message)
    
    def _run_interface_agent(self, instruction: str, chat_history: List[Dict[str, str]]) -> str:
        """Spustí interface agenta pro formátování odpovědi"""
        try:
            chat_history_str = self._format_chat_history(chat_history)
            
            task = Task(
                description=self.interface_task_template["description"].format(
                    instruction_from_strategist=instruction,
                    chat_history=chat_history_str
                ),
                expected_output=self.interface_task_template["expected_output"],
                agent=self.interface_agent
            )
            
            crew = Crew(
                agents=[self.interface_agent],
                tasks=[task],
                process=Process.sequential,
                verbose=False,
                memory=False
            )
            
            result = crew.kickoff()
            return str(result).strip()
            
        except Exception as e:
            logger.error(f"❌ Chyba při spuštění interface agenta: {e}")
            return "Omlouvám se, došlo k chybě při formátování odpovědi."
    
    def _run_canvas_agent(self, brief_state: BriefState) -> str:
        """Spustí canvas agenta pro vytvoření vizualizace"""
        try:
            brief_state_str = brief_state.model_dump_json(indent=2)
            
            task = Task(
                description=self.canvas_task_template["description"].format(
                    brief_state=brief_state_str
                ),
                expected_output=self.canvas_task_template["expected_output"],
                agent=self.canvas_agent
            )
            
            crew = Crew(
                agents=[self.canvas_agent],
                tasks=[task],
                process=Process.sequential,
                verbose=False,
                memory=False
            )
            
            result = crew.kickoff()
            return str(result).strip()
            
        except Exception as e:
            logger.error(f"❌ Chyba při spuštění canvas agenta: {e}")
            return self._generate_fallback_canvas(brief_state)
    
    def _format_chat_history(self, chat_history: List[Dict[str, str]], 
                           new_message: str = None) -> str:
        """Formátuje historii chatu pro agenty"""
        formatted = []
        
        for entry in chat_history:
            if entry.get("type") == "user":
                formatted.append(f"KLIENT: {entry['content']}")
            elif entry.get("type") == "agent":
                formatted.append(f"MATYLDA: {entry['content']}")
        
        if new_message:
            formatted.append(f"KLIENT: {new_message}")
        
        return "\n".join(formatted)
    
    def _create_fallback_strategist_response(self, brief_state: BriefState, user_message: str = "") -> StrategistResponse:
        """Vytvoří fallback odpověď strategického agenta"""

        # Pokus o základní analýzu zprávy
        message_lower = user_message.lower()

        if any(word in message_lower for word in ["anketa", "anketu", "průzkum", "výzkum"]):
            # Uživatel chce anketu/průzkum
            updated_state = brief_state.copy()
            updated_state.projekt_cil = "průzkum spokojenosti"

            return StrategistResponse(
                thought_process="Klient chce vytvořit anketu/průzkum. Nastavuji základní cíl a ptám se na detaily.",
                next_action="GATHER_INFO",
                target_field="cilova_skupina",
                suggested_instruction="Skvělé! Rozumím, že chcete vytvořit anketu. Abychom ji navrhli co nejefektivněji, potřebuji vědět, kdo bude vaší cílovou skupinou a jaké konkrétní téma vás zajímá.",
                updated_brief_state=updated_state,
                completion_percentage=20
            )
        else:
            # Obecný dotaz
            return StrategistResponse(
                thought_process="Obecný dotaz, ptám se na základní informace o projektu.",
                next_action="GATHER_INFO",
                target_field="projekt_cil",
                suggested_instruction="Rozumím. Abychom mohli pokračovat, potřebuji vědět, jaký je hlavní cíl vašeho projektu a jaké rozhodnutí má podpořit.",
                updated_brief_state=brief_state,
                completion_percentage=0
            )
    
    def _generate_fallback_canvas(self, brief_state: BriefState) -> str:
        """Generuje fallback canvas obsah"""
        timestamp = datetime.now().strftime("%d.%m.%Y %H:%M")
        
        return f"""# 📋 Projektové Zadání

## 🎯 Cíl Projektu
{brief_state.projekt_cil or "Zatím neurčeno"}

## 🔑 Klíčové Rozhodnutí
{brief_state.klicove_rozhodnuti or "Zatím neurčeno"}

## 👥 Cílová Skupina
{brief_state.cilova_skupina or "Zatím neurčeno"}

## 📊 Požadované Analýzy
{brief_state.pozadovane_analyzy or "Zatím neurčeno"}

## ⏱️ Časový Rámec
{brief_state.casovy_ramec or "Zatím neurčeno"}

## 💰 Rozpočet
{brief_state.rozpocet or "Zatím neurčeno"}

---
*Aktualizováno: {timestamp}*"""

# Globální instance
specialist_orchestrator = SpecialistOrchestrator()

def get_specialist_orchestrator() -> SpecialistOrchestrator:
    """Vrátí globální instanci specialist orchestrátoru"""
    return specialist_orchestrator

if __name__ == "__main__":
    # Test specialist orchestrátoru
    print("🧪 Testování SpecialistOrchestrator...")
    
    orchestrator = SpecialistOrchestrator()
    
    # Test zprávy
    test_brief = BriefState()
    test_history = []
    test_message = "Potřebuji udělat průzkum spokojenosti občanů."
    
    result = orchestrator.process_message(
        test_message, test_history, test_brief, "test-session"
    )
    
    print(f"✅ Chat odpověď: {result.chat_response}")
    print(f"📊 Canvas obsah: {result.canvas_content[:200]}...")
    print(f"🎯 Dokončeno: {result.completion_percentage}%")
