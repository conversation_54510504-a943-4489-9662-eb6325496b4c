# Matylda Backend

Backend systém pro Matylda - Strategický AI Partner postavený na LangChain frameworku.

## Přehled

Backend implementuje inteligentní konverzační systém pro strategické poradenství a výzkum. Využívá LangChain LCEL pro řízení dialogu, FastAPI pro REST API a Supabase pro RAG systém a perzistentní úložiště.

## Architektura

### Klíčové komponenty

- **ConversationalAgent** (`conversational_agent.py`) - Hlavní inteligentní agent řízený LangChain LCEL
- **Session Handler** (`session_handler.py`) - Správa stavových konverzací s automatickým zachováním informací
- **API Server** (`api_server.py`) - FastAPI server s REST endpointy
- **RAG System** (`rag_system.py`) - Retrieval-Augmented Generation pro znalostní bázi
- **Config Loader** (`config_loader.py`) - Dynamické načítání YAML konfigurací (legacy)

### <PERSON><PERSON><PERSON><PERSON><PERSON> struktura

```
backend/
├── conversational_agent.py   # LangChain ConversationalAgent - hlavní inteligence
├── session_handler.py        # Správa stavových konverzací
├── api_server.py             # FastAPI server
├── rag_system.py             # RAG systém pro znalosti
├── config_loader.py          # YAML konfigurace loader (legacy)
├── best_practices_system.py  # Systém best practices (legacy)
├── project_learning_tool.py  # Nástroj pro učení (legacy)
├── config.py                 # Základní konfigurace (legacy)
├── main.py                   # Jednorázové spuštění (legacy)
├── main_dynamic.py           # Dynamické spuštění (legacy)
├── interactive_main.py       # Dialog logika (legacy)
├── requirements.txt          # Python závislosti
├── agents/                   # YAML definice agentů (legacy)
│   └── onboarding_agents.yaml
├── tasks/                    # YAML definice úkolů (legacy)
│   └── onboarding_tasks.yaml
├── crews/                    # YAML definice posádek (legacy)
│   └── onboarding_crew.yaml
└── test_*.py                 # Test soubory
```

## Instalace a spuštění

### Předpoklady

- Python 3.8+
- OpenAI API klíč
- Supabase instance (volitelné pro RAG)

### Instalace závislostí

```bash
cd backend
pip install -r requirements.txt
```

### Konfigurace

Vytvořte `.env` soubor v root adresáři projektu:

```env
# OpenAI API
OPENAI_API_KEY="your-openai-api-key"

# Supabase (volitelné)
SUPABASE_URL="your-supabase-url"
SUPABASE_KEY="your-supabase-key"

# API Server
API_HOST=0.0.0.0
API_PORT=8001
API_RELOAD=True

# ConversationalAgent konfigurace
CONVERSATIONAL_MODEL=gpt-4
CONVERSATIONAL_TEMPERATURE=0.3
CONVERSATIONAL_MAX_TOKENS=2000
CONVERSATIONAL_VERBOSE=True

# Session Management
MAX_SESSION_ITERATIONS=20
SESSION_TIMEOUT_MINUTES=60

# RAG System
SUPABASE_TABLE_NAME=gdanalyst
RAG_SEARCH_RESULTS=5
RAG_EMBEDDING_MODEL=text-embedding-ada-002
```

### Spuštění

#### 🚀 Automatické spuštění (doporučeno)
```bash
# Z root adresáře projektu
./start_servers.sh
```

#### 🔧 Manuální spuštění API serveru
```bash
cd backend
source ../venv/bin/activate
python api_server.py
```

API bude dostupné na: http://localhost:8001 (nebo port z .env)
Dokumentace: http://localhost:8001/docs

#### ⚙️ Konfigurace portů
Porty lze změnit v `.env` souboru:
```env
API_PORT=8001
FRONTEND_PORT=8080
```

#### Test komponent
```bash
cd backend
# Test ConversationalAgent
python conversational_agent.py

# Test Session Handler
python session_handler.py
```

#### Legacy komponenty (nepoužívané)
```bash
cd backend
# Interaktivní demo (legacy)
python interactive_main.py

# Jednorázové spuštění (legacy)
python main.py
```

## API Endpointy

### Hlavní chat endpoint

#### POST /chat
Univerzální endpoint pro chat komunikaci.

**Request:**
```json
{
    "message": "Uživatelská zpráva",
    "session_id": "optional-session-id"
}
```

**Response:**
```json
{
    "session_id": "uuid",
    "question": "Další otázka od AI",
    "final_analysis": null,
    "status": "active",
    "is_complete": false
}
```

### Ostatní endpointy

- `GET /` - Health check
- `GET /health` - Detailní health check
- `GET /sessions` - Seznam aktivních sessions
- `GET /conversation/{session_id}/status` - Status konkrétní session
- `DELETE /conversation/{session_id}` - Smazání session

### Legacy endpointy (deprecated)

- `POST /conversation/start` - Zahájení konverzace
- `POST /conversation/answer` - Zpracování odpovědi

## Konfigurace ConversationalAgent

Agent je konfigurován přímo v kódu a přes environment variables:

### Systémový Prompt
Chování agenta je definováno v `conversational_agent.py` v metodě `_create_system_prompt()`:

```python
def _create_system_prompt(self) -> str:
    return """Jsi Matylda, geniální strategický konzultant...

    # PRAVIDLA CHOVÁNÍ
    - Vždy buď empatický, profesionální a proaktivní
    - Pravidlo pro cenu: Neodpovídej na dotazy o ceně

    # PRAVIDLA VÝSTUPU
    Tvůj výstup MUSÍ BÝT validní JSON objekt...
    """
```

### Environment Variables
```env
CONVERSATIONAL_MODEL=gpt-4
CONVERSATIONAL_TEMPERATURE=0.3
CONVERSATIONAL_MAX_TOKENS=2000
```

### Legacy YAML konfigurace
YAML soubory v `agents/`, `tasks/`, `crews/` jsou zachovány pro kompatibilitu, ale nejsou aktivně používány.

## RAG Systém

RAG (Retrieval-Augmented Generation) systém poskytuje agentům přístup k znalostní bázi:

### Konfigurace
```env
SUPABASE_URL="your-supabase-url"
SUPABASE_KEY="your-supabase-key"
SUPABASE_TABLE_NAME="knowledge_table"
RAG_SEARCH_RESULTS=5
```

### Použití
```python
from rag_system import knowledge_base_search

# Vyhledání v znalostní bázi
results = knowledge_base_search("dotaz")
```

## Best Practices Systém

Systém pro ukládání a využívání osvědčených postupů:

```python
from best_practices_system import BestPracticesSystem

bp_system = BestPracticesSystem()
bp_system.save_practice("kategorie", "popis", "kontext")
practices = bp_system.get_relevant_practices("dotaz")
```

## Testování

### Spuštění testů

```bash
# Test API
cd backend
python test_api.py

# Test best practices
python test_best_practices_crew.py

# Test rozšířené posádky
python test_extended_crew.py

# Test struktury
python test_structure.py
```

### Test API s curl

```bash
# Health check
curl http://localhost:8001/health

# Chat
curl -X POST http://localhost:8001/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Potřebuji udělat průzkum"}'
```

## Vývoj

### Úprava chování agenta

1. Upravte systémový prompt v `conversational_agent.py`
2. Přidejte nová pole do `ConversationState` modelu
3. Aktualizujte validační logiku v `_validate_state_preservation()`
4. Otestujte změny pomocí `python conversational_agent.py`

### Přidání nových funkcí

1. Rozšiřte `ConversationState` model o nová pole
2. Upravte systémový prompt pro práci s novými daty
3. Aktualizujte frontend pro zobrazení nových informací
4. Přidejte testy pro novou funkcionalitu

### Debugging

```env
# Zapnutí verbose módu
CONVERSATIONAL_VERBOSE=True
CONVERSATIONAL_LOG_LEVEL=DEBUG
LOG_LEVEL=DEBUG
```

### Přidání nového nástroje (RAG)

1. Rozšiřte `rag_system.py` o novou funkci
2. Přidejte volání do systémového promptu
3. Otestujte integraci

## Produkční nasazení

### Docker (doporučeno)

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY backend/ .
RUN pip install -r requirements.txt

EXPOSE 8001
CMD ["python", "api_server.py"]
```

### Systemd service

```ini
[Unit]
Description=Matylda Backend API
After=network.target

[Service]
Type=simple
User=matylda
WorkingDirectory=/opt/matylda/backend
ExecStart=/opt/matylda/venv/bin/python api_server.py
Restart=always

[Install]
WantedBy=multi-user.target
```

### Nginx reverse proxy

```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## Monitoring

- Health check endpoint: `/health`
- Logs: Standardní Python logging
- Metriky: Počet aktivních sessions v `/health`

## Bezpečnost

- API klíče v environment variables
- Input validace přes Pydantic
- Rate limiting (konfigurovatelné)
- CORS nastavení pro produkci

## Troubleshooting

### Časté problémy

1. **OpenAI API chyby**: Zkontrolujte API klíč a kredit
2. **Supabase připojení**: Ověřte URL a klíč
3. **Port konflikty**: Změňte `API_PORT` v `.env`
4. **Memory issues**: Nastavte `MAX_ACTIVE_SESSIONS`

### Logy

```bash
# Spuštění s debug logováním
LOG_LEVEL=DEBUG python api_server.py
```
