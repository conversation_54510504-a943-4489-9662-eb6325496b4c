#!/usr/bin/env python3
"""
Skript pro nastavení best practices tabulky v Supabase
Spustí SQL příkazy pro vytvoření tabulky a funkcí
"""

import os
from supabase import create_client, Client
from dotenv import load_dotenv

# Načtení konfigurace
load_dotenv()

def setup_best_practices_table():
    """Vytvoří tabulku best_practices a související funkce v Supabase"""
    
    # Inicializace Supabase klienta
    url = os.getenv('SUPABASE_URL')
    key = os.getenv('SUPABASE_KEY')  # Použijeme existující klíč z .env
    
    if not url or not key:
        print("❌ Chybí Supabase credentials (SUPABASE_URL, SUPABASE_KEY)")
        return False
    
    try:
        supabase: Client = create_client(url, key)
        print("✅ Připojen k Supabase")
        
        # <PERSON>kusí<PERSON> nejprve otestovat, zda tabulka už existuje
        try:
            result = supabase.table('best_practices').select('id').limit(1).execute()
            print("✅ Tabulka best_practices už existuje")
        except Exception as e:
            print(f"⚠️ Tabulka best_practices neexistuje nebo není dostupná: {e}")
            print("📝 Prosím vytvořte tabulku manuálně v Supabase dashboard pomocí SQL:")
            print("""
CREATE TABLE IF NOT EXISTS best_practices (
    id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at timestamptz DEFAULT now(),
    context_description text NOT NULL,
    successful_strategy text NOT NULL,
    associated_agent_role text NOT NULL,
    success_rating float4 DEFAULT 0.95,
    feedback_notes text[] DEFAULT '{}',
    embedding vector(1536)
);

-- Zapnutí Row Level Security
ALTER TABLE best_practices ENABLE ROW LEVEL SECURITY;

-- Policies
CREATE POLICY "Enable read access for all users" ON best_practices
    FOR SELECT USING (true);
CREATE POLICY "Enable insert access for all users" ON best_practices
    FOR INSERT WITH CHECK (true);
CREATE POLICY "Enable update access for all users" ON best_practices
    FOR UPDATE USING (true);

-- Indexy
CREATE INDEX IF NOT EXISTS idx_best_practices_agent_role ON best_practices(associated_agent_role);
CREATE INDEX IF NOT EXISTS idx_best_practices_success_rating ON best_practices(success_rating);
CREATE INDEX IF NOT EXISTS idx_best_practices_created_at ON best_practices(created_at);

-- Funkce pro sémantické vyhledávání
CREATE OR REPLACE FUNCTION match_best_practices (
  query_embedding vector(1536),
  match_threshold float,
  match_count int
)
RETURNS TABLE (
  id uuid,
  context_description text,
  successful_strategy text,
  associated_agent_role text,
  similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    bp.id,
    bp.context_description,
    bp.successful_strategy,
    bp.associated_agent_role,
    1 - (bp.embedding <=> query_embedding) as similarity
  FROM
    best_practices bp
  WHERE 1 - (bp.embedding <=> query_embedding) > match_threshold
  ORDER BY
    similarity DESC
  LIMIT match_count;
END;
$$;
            """)
            return False
        
        # Pokud tabulka existuje, zkusíme otestovat funkci match_best_practices
        try:
            # Test volání funkce (s dummy daty)
            dummy_embedding = [0.0] * 1536  # Dummy embedding
            result = supabase.rpc('match_best_practices', {
                'query_embedding': dummy_embedding,
                'match_threshold': 0.5,
                'match_count': 1
            }).execute()
            print("✅ Funkce match_best_practices je dostupná")
        except Exception as e:
            print(f"⚠️ Funkce match_best_practices není dostupná: {e}")
            print("📝 Prosím vytvořte funkci manuálně v Supabase dashboard")
        
        print("\n🎉 Best practices systém úspěšně nastaven v Supabase!")
        return True
        
    except Exception as e:
        print(f"❌ Chyba při nastavování databáze: {e}")
        return False

def test_best_practices_table():
    """Otestuje, zda tabulka funguje správně"""
    
    url = os.getenv('SUPABASE_URL')
    key = os.getenv('SUPABASE_KEY')
    
    if not url or not key:
        print("❌ Chybí Supabase credentials pro test")
        return False
    
    try:
        supabase: Client = create_client(url, key)
        
        # Test vložení testovacího záznamu
        test_data = {
            'context_description': 'Test situace pro ověření funkcionality',
            'successful_strategy': 'Test strategie',
            'associated_agent_role': 'TestAgent',
            'success_rating': 0.9,
            'feedback_notes': ['Test poznámka']
        }
        
        result = supabase.table('best_practices').insert(test_data).execute()
        
        if result.data:
            test_id = result.data[0]['id']
            print(f"✅ Test záznam vložen s ID: {test_id}")
            
            # Test čtení
            read_result = supabase.table('best_practices').select('*').eq('id', test_id).execute()
            if read_result.data:
                print("✅ Test čtení úspěšný")
                
                # Smazání test záznamu
                supabase.table('best_practices').delete().eq('id', test_id).execute()
                print("✅ Test záznam smazán")
                
                return True
        
        return False
        
    except Exception as e:
        print(f"❌ Chyba při testování: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Nastavování Best Practices systému v Supabase...")
    
    if setup_best_practices_table():
        print("\n🧪 Testování funkcionality...")
        if test_best_practices_table():
            print("\n✅ Best Practices systém je plně funkční!")
        else:
            print("\n⚠️ Systém je nastaven, ale test selhal")
    else:
        print("\n❌ Nastavení selhalo")
