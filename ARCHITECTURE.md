# ARCHITEKTURA SYSTÉMU "MATYLDA" v2.0

Systém Matylda v2.0 je navržen jako **univerzální š<PERSON>lovatelná platforma** postavená na mikroservisní architektuře s plnou kontrolou nad dialogem a neomezenou škálovatelností napříč business doménami.

**🚀 Verze 2.0:** Kompletní transformace na univerzální platformu s mikroservisy, API Gateway, multi-domain RAG systémem a domain-aware best practices. Podporuje neomezený počet specializovaných domén s izolovanými znalostními bázemi.

## 🏗️ Architektonické Principy v2.0

### 1. Univerzální <PERSON>lnost
- **Multi-Domain Support** - Neomezený počet specializovaných domén
- **Horizontal Scaling** - Přidání nové domény během 30 minut
- **Domain Isolation** - 100% separace dat a logiky mezi doménami
- **Configuration-Driven** - Vše řízeno YAML soubory

### 2. Mikroservisní Architektura
- **API Gateway** - Centrální routing pro všechny služby
- **Service Isolation** - Nezávislé služby (chat, knowledge, best_practices, analytics, admin)
- **Clean Interfaces** - Jasné kontrakty mezi službami
- **Health Monitoring** - Kompletní monitoring všech komponent

### 3. "Brain & Face" Paradigma
- **Backend "Brain"** - Centrální inteligence s komplexní logikou
- **Frontend "Face"** - Jednoduché rozhraní pro prezentaci
- **API Gateway** - Inteligentní routing mezi službami

### 4. Domain-Aware Intelligence
- **Specialized Agents** - Tým tří specialistů pro každou doménu
- **Isolated Knowledge** - Vlastní znalostní báze pro každou doménu
- **Context-Aware Best Practices** - Osvědčené postupy podle domény a kontextu
- **Cross-Domain Relationships** - Inteligentní sdílení mezi souvisejícími doménami

## 🧠 Klíčové Komponenty v2.0

### MatyldaOrchestrator
**Umístění:** `backend/matylda_orchestrator.py`

Univerzální orchestrátor pro dynamické sestavování týmů:
- **Domain Detection:** Automatické určení domény na základě požadavku
- **Team Assembly:** Sestavení specializovaného týmu pro danou doménu
- **Fallback Mechanisms:** Robustní zpracování neaktivních domén
- **Configuration Management:** Načítání domén z YAML konfigurace

```python
class MatyldaOrchestrator:
    def determine_domain(self, message: str, request_type: str) -> str
    def process_request(self, initial_request: str, request_type: str) -> Dict[str, Any]
    def get_available_domains(self) -> List[Dict[str, Any]]
```

### API Gateway
**Umístění:** `backend/api_gateway.py`

Centrální vstupní bod pro všechny mikroservisy:
- **Service Routing:** Inteligentní směrování na správné služby
- **Request Validation:** Validace a transformace požadavků
- **Health Monitoring:** Monitoring stavu všech služeb
- **Error Handling:** Centralizované zpracování chyb

```python
class MatyldaAPIGateway:
    def route_request(self, service: str, request: GatewayRequest) -> GatewayResponse
    def get_service_health(self) -> Dict[str, Any]
```

### Multi-Domain RAG System
**Umístění:** `backend/multi_domain_rag.py`

Pokročilý systém znalostních bází s plnou izolací domén:
- **Domain Isolation:** Separátní znalostní báze pro každou doménu
- **15 Knowledge Bases:** Napříč 5 doménami (onboarding, sales, participace, support, shared)
- **Vector Stores:** Supabase s optimalizovanými indexy
- **Cross-Domain Search:** Inteligentní vyhledávání napříč souvisejícími doménami

```python
class MultiDomainRAG:
    def search_knowledge_base(self, query: str, domain: str, k: int) -> str
    def get_available_knowledge_bases(self, domain: str) -> List[Dict]
    def get_status(self) -> Dict[str, Any]
```

### Specialist Orchestrator
**Umístění:** `backend/specialist_orchestrator.py`

Orchestrátor týmu tří specializovaných agentů:
- **Strategic Onboarder:** Logický a strategický motor
- **Interface Agent:** Empatický komunikační specialista
- **Canvas Architect:** Vizuální syntetizátor projektového zadání
- **Parallel Processing:** Současné zpracování všemi agenty

```python
class SpecialistOrchestrator:
    def orchestrate_specialists(self, session_id: str, user_message: str, chat_history: List) -> Dict
    def _process_strategic_analysis(self, ...) -> Dict
    def _process_interface_response(self, ...) -> str
    def _process_canvas_update(self, ...) -> str
```

### Domain-Aware Best Practices
**Umístění:** `backend/best_practices_system.py`

Inteligentní systém osvědčených postupů:
- **Domain Context:** Vyhledávání podle domény a kontextu
- **Cross-Domain Relationships:** Sdílení mezi souvisejícími doménami
- **Usage Analytics:** Sledování použití a efektivity
- **Database Integration:** Pokročilé vyhledávací funkce v Supabase

```python
class MatyldaBestPracticesSystem:
    def search_best_practices(self, query: str, domain: str, context: str) -> List[BestPractice]
    def get_domain_statistics(self, domain: str) -> Dict[str, Any]
    def get_related_domains(self, domain: str) -> List[Dict[str, Any]]
```

## 🔄 Tok Dat v2.0

### 1. Zahájení Konverzace přes API Gateway
```
Frontend → POST /gateway/chat {"message": "...", "request_type": "..."}
         → API Gateway určí službu (chat)
         → MatyldaOrchestrator určí doménu
         → Sestaví tým specialistů pro doménu
         → Vrátí duální odpověď (chat_response + canvas_content)
```

### 2. Pokračování Konverzace
```
Frontend → POST /gateway/chat {"message": "...", "session_id": "..."}
         → API Gateway směruje na chat službu
         → SpecialistOrchestrator zpracuje s historií
         → Tři agenti pracují paralelně
         → Vrátí aktualizovaný stav + canvas
```

### 3. Vyhledávání ve Znalostních Bázích
```
Frontend → POST /gateway/knowledge {"message": "...", "domain": "..."}
         → API Gateway směruje na knowledge službu
         → Multi-Domain RAG vyhledá v příslušné doméně
         → Vrátí relevantní znalosti
```

### 4. Best Practices Dotazy
```
Frontend → POST /gateway/best_practices {"message": "...", "domain": "...", "context": "..."}
         → API Gateway směruje na best_practices službu
         → Domain-aware vyhledávání
         → Vrátí osvědčené postupy
```

### 5. Analytics a Monitoring
```
Frontend → POST /gateway/analytics {"domain": "..."}
         → API Gateway směruje na analytics službu
         → Agregace statistik ze všech služeb
         → Vrátí kompletní přehled systému
```

## 📊 Datové Modely v2.0

### GatewayRequest
```python
class GatewayRequest(BaseModel):
    message: str
    session_id: Optional[str]
    domain: Optional[str]
    request_type: Optional[str]
    context: Optional[str]
    metadata: Optional[Dict[str, Any]]
```

### GatewayResponse
```python
class GatewayResponse(BaseModel):
    success: bool
    data: Optional[Dict[str, Any]]
    error: Optional[str]
    domain: Optional[str]
    service: Optional[str]
    metadata: Optional[Dict[str, Any]]
```

### Domain Configuration
```python
class DomainConfig(BaseModel):
    id: str
    name: str
    description: str
    enabled: bool
    keywords: List[str]
    orchestrator_config: Dict[str, str]
```

### Knowledge Base Configuration
```python
class KnowledgeBaseConfig(BaseModel):
    id: str
    table_name: str
    query_function: str
    description: str
    domain: str
    categories: List[str]
```

### Best Practice Model
```python
class BestPractice(BaseModel):
    id: str
    title: str
    category: str
    description: str
    context: str
    domain: str
    effectiveness_score: float
    usage_count: int
```

## 🛡️ Bezpečnostní Prvky

### 1. Validace Stavů
- Automatická kontrola ztráty informací
- Inteligentní sloučení stavů
- Logování varování při problémech

### 2. Error Handling
- Fallback mechanismy pro JSON parsing
- Graceful degradation při chybách
- Informativní chybové zprávy

### 3. Pravidla Chování
- Striktní pravidla pro dotazy na cenu
- Kontrola výstupního formátu
- Ochrana proti nevalidním odpovědím

## 🔧 Konfigurace v2.0

### Hlavní Konfigurační Soubory

#### Domény: `backend/config/domains.yaml`
```yaml
domains:
  onboarding_pruzkumy:
    name: "Onboarding Průzkumů"
    description: "Specializace na přípravu a plánování výzkumných projektů"
    enabled: true
    keywords: ["průzkum", "výzkum", "spokojenost"]
```

#### Znalostní Báze: `backend/config/domain_knowledge_bases.yaml`
```yaml
onboarding_pruzkumy:
  knowledge_bases:
    - id: "metodologie_pruzkumy"
      table_name: "kb_onboarding_pruzkumy"
      query_function: "match_onboarding_pruzkumy"
```

#### Environment Variables: `.env`
```env
# API Gateway
API_HOST=0.0.0.0
API_PORT=8001

# LLM Models
DEFAULT_LLM_MODEL=gpt-4o-mini
CONVERSATIONAL_TEMPERATURE=0.3

# Multi-Domain RAG
ENABLE_MULTI_DOMAIN_RAG=True
DEFAULT_SIMILARITY_THRESHOLD=0.78

# Supabase
SUPABASE_URL=...
SUPABASE_KEY=...

# Orchestrator
ENABLE_MATYLDA_ORCHESTRATOR=True
DEFAULT_DOMAIN=onboarding_pruzkumy
```

## 🚀 Výhody Architektury v2.0

1. **Univerzální Škálovatelnost:** Neomezený počet domén
2. **Mikroservisní Design:** Nezávislé škálování služeb
3. **Domain Isolation:** 100% separace dat mezi doménami
4. **Configuration-Driven:** Přidání domény během 30 minut
5. **Production-Ready:** Kompletní monitoring a health checks
6. **API Gateway:** Centrální routing a service discovery
7. **Multi-Domain RAG:** Izolované znalostní báze
8. **Best Practices:** Domain-aware osvědčené postupy

## 📈 Současný Stav Domén

### Aktivní Domény
- ✅ **Onboarding Průzkumů** - Plně funkční

### Připravené Domény
- 🔄 **Sales Qualification** - Konfigurace hotova
- 🔄 **Participativní Rozpočet** - Konfigurace hotova
- 🔄 **Customer Support** - Konfigurace hotova

### Infrastruktura
- ✅ **15 Znalostních Bází** - Napříč všemi doménami
- ✅ **API Gateway** - Port 8002
- ✅ **Backend API** - Port 8001
- ✅ **Frontend** - Port 8082
- ✅ **Health Monitoring** - Všechny služby

Systém je připraven na exponenciální růst s minimálními náklady na přidání nových domén.