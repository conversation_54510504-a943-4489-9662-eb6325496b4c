# Migration Guide: Matylda v1.0 → v2.0

## 🎯 <PERSON><PERSON><PERSON>led Migrace

Tento dokument popisuje přechod z Matylda v1.0 (LangChain-based single agent) na v2.0 (univerzá<PERSON><PERSON>atelná platforma s mikroservisy).

## 🔄 Klíčové Změny

### Architektonické Transformace

#### v1.0 → v2.0
| Komponenta | v1.0 | v2.0 |
|------------|------|------|
| **Architektura** | Single agent | Mikroservisní platforma |
| **Agenti** | 1 ConversationalAgent | 3 specializované agenty × N domén |
| **Domény** | 1 (onboarding) | Neomezené (4 připravené) |
| **RAG** | Single knowledge base | 15 izolovaných znalostních bází |
| **API** | Single endpoint | API Gateway + 5 mikroservisů |
| **Best Practices** | File-based | Database + domain-aware |
| **Šk<PERSON>lovatelnost** | Limitovaná | Horizontální |

### Nové Komponenty v2.0

1. **MatyldaOrchestrator** - Univerzální orchestrátor
2. **API Gateway** - Mikroservisní routing
3. **Multi-Domain RAG** - Izolované znalostní báze
4. **Specialist Orchestrator** - Tým 3 agentů
5. **Domain-Aware Best Practices** - Kontextové postupy

## 📋 Migrace Krok za Krokem

### Krok 1: Backup Současného Stavu

```bash
# Backup konfigurace
cp .env .env.v1.backup
cp -r backend/agents backend/agents.v1.backup
cp -r backend/tasks backend/tasks.v1.backup
cp -r backend/crews backend/crews.v1.backup

# Backup databáze (pokud máte data)
# Exportujte současná data ze Supabase
```

### Krok 2: Aktualizace Konfigurace

#### 2.1 Nové Environment Variables
Přidejte do `.env`:

```env
# Multi-Domain RAG
ENABLE_MULTI_DOMAIN_RAG=True
DEFAULT_SIMILARITY_THRESHOLD=0.78
MAX_KNOWLEDGE_RESULTS=5

# Orchestrator
ENABLE_MATYLDA_ORCHESTRATOR=True
DEFAULT_DOMAIN=onboarding_pruzkumy

# Best Practices
ENABLE_BEST_PRACTICES=True

# Session Management (nové)
MAX_ACTIVE_SESSIONS=100
SESSION_TIMEOUT_MINUTES=60
```

#### 2.2 Nové Konfigurační Soubory
Vytvořte nové konfigurace:

```bash
# Domény
cp backend/config/domains.yaml.example backend/config/domains.yaml

# Znalostní báze
cp backend/config/domain_knowledge_bases.yaml.example backend/config/domain_knowledge_bases.yaml
```

### Krok 3: Databázová Migrace

#### 3.1 Supabase Schema Update
Spusťte nové SQL skripty:

```sql
-- Spusťte v Supabase SQL editoru
\i backend/sql/domain_knowledge_bases.sql
\i backend/sql/domain_best_practices.sql
```

#### 3.2 Migrace Existujících Dat
```bash
# Pokud máte existující data v gdanalyst tabulce
# Spusťte migration script (bude vytvořen)
python backend/migrate_v1_to_v2.py
```

### Krok 4: Aktualizace Kódu

#### 4.1 API Endpoints
Staré endpointy zůstávají funkční, nové jsou dostupné:

| v1.0 | v2.0 | Status |
|------|------|--------|
| `POST /chat` | `POST /chat/universal` | ✅ Kompatibilní |
| - | `POST /gateway/chat` | 🆕 Nový |
| - | `POST /gateway/knowledge` | 🆕 Nový |
| - | `POST /gateway/best_practices` | 🆕 Nový |
| - | `POST /gateway/analytics` | 🆕 Nový |

#### 4.2 Response Format
Nový formát odpovědi zahrnuje canvas:

```json
{
  "session_id": "...",
  "chat_response": "...",
  "canvas_content": "...",  // NOVÉ
  "is_complete": false,
  "completion_percentage": 25,
  "domain": "onboarding_pruzkumy",  // NOVÉ
  "domain_name": "Onboarding Průzkumů"  // NOVÉ
}
```

### Krok 5: Frontend Aktualizace

#### 5.1 Duální Rozhraní
Frontend nyní podporuje duální rozhraní:
- **Dialog Panel** - Konverzace (vlevo)
- **Canvas Panel** - Strukturovaný obsah (vpravo)

#### 5.2 Nové API Volání
```javascript
// v1.0
fetch('/chat', { ... })

// v2.0 (kompatibilní)
fetch('/chat/universal', { ... })

// v2.0 (nové)
fetch('/gateway/chat', { ... })
```

### Krok 6: Testování Migrace

#### 6.1 Funkční Testy
```bash
# Test kompatibility v1.0 endpointů
curl -X POST http://localhost:8001/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "test"}'

# Test nových v2.0 endpointů
curl -X POST http://localhost:8001/chat/universal \
  -H "Content-Type: application/json" \
  -d '{"message": "test", "request_type": "průzkum"}'

# Test API Gateway
curl -X POST http://localhost:8002/gateway/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "test", "request_type": "průzkum"}'
```

#### 6.2 Validace Dat
```bash
# Zkontrolujte, že všechny domény jsou dostupné
curl http://localhost:8001/health

# Zkontrolujte znalostní báze
curl http://localhost:8002/gateway/analytics
```

## 🔧 Řešení Problémů

### Časté Problémy při Migraci

#### 1. Chybějící Supabase Funkce
**Problém:** `Could not find function match_onboarding_pruzkumy`

**Řešení:**
```sql
-- Spusťte v Supabase
\i backend/sql/domain_knowledge_bases.sql
```

#### 2. Nekompatibilní Response Format
**Problém:** Frontend očekává starý formát

**Řešení:** Použijte kompatibilní endpoint `/chat` místo `/chat/universal`

#### 3. Chybějící Konfigurace
**Problém:** `FileNotFoundError: domains.yaml`

**Řešení:**
```bash
cp backend/config/domains.yaml.example backend/config/domains.yaml
```

#### 4. Port Konflikty
**Problém:** Nové porty 8002 (Gateway) jsou obsazené

**Řešení:**
```bash
# Změňte porty v .env nebo ukončete konfliktní procesy
./stop_servers.sh
```

## 📊 Výhody po Migraci

### Okamžité Výhody
- ✅ **Zpětná kompatibilita** - Stávající integrace fungují
- ✅ **Duální rozhraní** - Lepší uživatelský zážitek
- ✅ **Robustnější architektura** - Mikroservisy
- ✅ **Lepší monitoring** - Health checks

### Dlouhodobé Výhody
- 🚀 **Škálovatelnost** - Přidání domény během 30 minut
- 🏗️ **Modulárnost** - Nezávislé služby
- 📚 **Izolované znalosti** - Žádná kontaminace mezi doménami
- ⭐ **Best practices** - Domain-aware postupy

## 🎯 Doporučený Postup

### Fáze 1: Příprava (1 den)
1. Backup současného stavu
2. Aktualizace konfigurace
3. Spuštění SQL skriptů

### Fáze 2: Migrace (1 den)
1. Aktualizace kódu
2. Testování kompatibility
3. Validace funkcí

### Fáze 3: Optimalizace (1 týden)
1. Přechod na nové endpointy
2. Využití nových funkcí
3. Přidání dalších domén

## 📞 Podpora

V případě problémů při migraci:

1. **Zkontrolujte logy:** `tail -f logs/backend.log`
2. **Ověřte konfiguraci:** Porovnejte s `.env.example`
3. **Testujte postupně:** Začněte s kompatibilními endpointy
4. **Dokumentace:** Viz `docs/v2_manual.md`

Migrace na Matylda v2.0 přináší významné výhody v škálovatelnosti a funkcionalitě při zachování zpětné kompatibility! 🚀
