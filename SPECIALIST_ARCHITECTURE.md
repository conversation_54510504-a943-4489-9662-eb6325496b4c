# Architektura "Týmu Specialistů" - Matylda v2.0

## 🎯 Přehled

Nová architektura "<PERSON><PERSON><PERSON> <PERSON>" nahrazuje původní rigidní CrewAI přístup flexibilním systémem tří specializovaných agentů, kte<PERSON><PERSON> s<PERSON>up<PERSON>uj<PERSON> v reálném čase pro dosažení vysoké kvality onboardingu klientů.

## 🏗️ Architektonické Principy

### 1. Specializace vs. Univerzálnost
- **Před:** Jeden univerzální agent se snažil zvládnout všechno
- **Nyní:** Tři specializovaní agenti, každý expert ve své oblasti

### 2. Paralelní vs. Sekvenčn<PERSON> Z<PERSON>
- **Před:** Rigidní sekvenční workflow
- **Nyní:** Strategické rozhodnutí následované paralelním zpracováním

### 3. Šk<PERSON>lovatelnost podle Domén
- **Před:** Jeden RAG systém pro všechno
- **Nyní:** Multi-domain RAG s izolovanými znalostními bázemi

## 🤖 Tři Specializovaní Agenti

### 1. Strategic Onboarder (strategic_onboarder_v1)
**Role:** Logický a Strategický Motor Onboardingu

**Odpovědnosti:**
- Analyzuje historii konverzace a stav briefu
- Rozhoduje o nejlepším dalším logickém kroku
- Aktualizuje stav briefu na základě nových informací
- Určuje procento dokončení

**Výstup:** JSON s rozhodnutím a aktualizovaným stavem

### 2. Interface Agent (interface_agent_v1)
**Role:** Empatický Komunikační Specialista

**Odpovědnosti:**
- Transformuje logické pokyny na přirozený dialog
- Zajišťuje empatickou a zdvořilou komunikaci
- Implementuje komunikační principy (potvrzení, empatie, jasnost)
- Přizpůsobuje tón podle situace

**Výstup:** Finální textová zpráva pro klienta

### 3. Canvas Architect (canvas_architect_v1)
**Role:** Vizuální Syntetizátor Projektového Zadání

**Odpovědnosti:**
- Vytváří a aktualizuje vizuální reprezentaci stavu projektu
- Generuje profesionální Markdown dokumenty
- Zobrazuje progres dokončení
- Udržuje "jediný zdroj pravdy" o stavu

**Výstup:** Strukturovaný Markdown dokument

## 🔄 Workflow Orchestrace

```mermaid
graph TD
    A[User Message] --> B[SpecialistOrchestrator]
    B --> C[Strategic Onboarder]
    C --> D{Rozhodnutí}
    D --> E[Interface Agent]
    D --> F[Canvas Architect]
    E --> G[Chat Response]
    F --> H[Canvas Content]
    G --> I[Unified Response]
    H --> I
```

### Krok 1: Strategické Rozhodnutí
1. Analýza historie chatu a stavu briefu
2. Identifikace chybějících informací
3. Rozhodnutí o dalším kroku (GATHER_INFO, CLARIFY, SUGGEST, COMPLETE)
4. Aktualizace stavu briefu

### Krok 2: Paralelní Zpracování
- **Interface Agent:** Formátuje odpověď pro klienta
- **Canvas Agent:** Aktualizuje vizuální stav

### Krok 3: Unifikace Výsledků
- Kombinace chat odpovědi a canvas obsahu
- Kontrola dokončení konverzace
- Vrácení strukturované odpovědi

## 📊 Datové Modely

### BriefState
```python
class BriefState(BaseModel):
    projekt_cil: Optional[str] = None
    klicove_rozhodnuti: Optional[str] = None
    cilova_skupina: Optional[str] = None
    pozadovane_analyzy: Optional[str] = None
    casovy_ramec: Optional[str] = None
    rozpocet: Optional[str] = None
    dalsi_poznamky: Optional[str] = None
```

### OrchestrationResult
```python
class OrchestrationResult(BaseModel):
    session_id: str
    chat_response: str
    canvas_content: str
    is_complete: bool
    brief_state: BriefState
    completion_percentage: int
```

## 🧠 Multi-Domain RAG Systém

### Konfigurace Znalostních Bází
```yaml
knowledge_bases:
  - id: "pruzkumy_municipality"
    table_name: "kb_pruzkumy"
    query_function: "match_pruzkumy"
    description: "Znalosti o municipálních průzkumech"
    domain: "onboarding_pruzkumy"
```

### Výhody
- **Izolace:** Každá doména má své vlastní znalosti
- **Škálovatelnost:** Snadné přidání nových domén
- **Relevance:** Přesnější vyhledávání podle kontextu

## 🔧 API Rozšíření

### Dualní Rozhraní
```json
{
  "session_id": "uuid",
  "chat_response": "Odpověď pro chat",
  "canvas_content": "Markdown obsah pro canvas",
  "completion_percentage": 75,
  "is_complete": false
}
```

### Kompatibilita
- Zachovány původní pole (`question`, `message`) pro zpětnou kompatibilitu
- Nová pole (`chat_response`, `canvas_content`) pro rozšířené funkce

## 📁 Struktura Souborů

```
backend/
├── agents/
│   ├── specialist_agents.yaml      # Nové specializované agenty
│   └── onboarding_agents.yaml      # Původní agenti (zachováno)
├── tasks/
│   ├── specialist_tasks.yaml       # Nové specializované úkoly
│   └── onboarding_tasks.yaml       # Původní úkoly (zachováno)
├── crews/
│   └── specialist_crew.yaml        # Konfigurace nové posádky
├── specialist_orchestrator.py      # Hlavní orchestrátor
├── multi_domain_rag.py            # Multi-domain RAG systém
└── session_handler.py             # Aktualizovaný session handler
```

## 🚀 Výhody Nové Architektury

### 1. Vyšší Kvalita Výstupů
- Každý agent je expert ve své oblasti
- Lepší konzistence a profesionalita

### 2. Flexibilita
- Snadné přizpůsobení jednotlivých komponent
- Možnost paralelního vývoje

### 3. Škálovatelnost
- Multi-domain RAG pro různé obory
- Snadné přidání nových agentů

### 4. Udržovatelnost
- Jasné oddělení odpovědností
- Modulární design

### 5. Uživatelský Zážitek
- Dualní rozhraní (chat + canvas)
- Vizuální progres dokončení
- Konzistentní komunikace

## 🔄 Migrace z Původního Systému

### Zachovaná Kompatibilita
- API endpoints zůstávají stejné
- Původní response formát je podporován
- Session management funguje stejně

### Nové Funkce
- Canvas obsah v response
- Procento dokončení
- Multi-domain RAG
- Specializovaní agenti

## 📈 Metriky Úspěchu

### Kvalita Konverzace
- Konzistence odpovědí
- Empatická komunikace
- Logická progrese

### Technická Výkonnost
- Rychlost odpovědi
- Spolehlivost systému
- Škálovatelnost

### Uživatelský Zážitek
- Vizuální feedback
- Jasnost komunikace
- Dokončovací míra

## 🔮 Budoucí Rozšíření

### Nové Domény
- Sales qualification
- Customer support
- Technical consulting

### Pokročilé Funkce
- Adaptivní agenti podle kontextu
- Učení z interakcí
- Personalizace podle klienta

---

*Dokumentace vytvořena: 10.07.2025*
*Verze: 2.0*
