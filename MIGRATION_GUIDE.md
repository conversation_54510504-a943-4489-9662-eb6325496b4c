# Migration Guide: P<PERSON>echod na Architekturu "Týmu Specialistů"

## 🎯 Přehled Migrace

Tento dokument popisuje přechod z původní ConversationalAgent architektury na novou architekturu "Týmu Specialistů" s třemi specializovanými agenty.

## 📋 Kontrolní Seznam Migrace

### ✅ Dokončené Kroky

1. **Vytvoření nových agentů**
   - `strategic_onboarder_v1` - strategick<PERSON> r<PERSON>
   - `interface_agent_v1` - komunikace s klientem  
   - `canvas_architect_v1` - vizualizace stavu

2. **Implementace SpecialistOrchestrator**
   - Nahrazuje ConversationalAgent
   - Řídí spolupráci tří agentů
   - Paralelní zpracování interface a canvas

3. **Multi-Domain RAG systém**
   - Podpora více izolovaných znalostních bází
   - Konfigurace podle domén
   - Rozšířené best practices s doménami

4. **API rozšíření**
   - <PERSON><PERSON><PERSON> (chat + canvas)
   - Zachovaná zpětná kompatibilita
   - Nová pole pro rozšířené funkce

5. **Session Handler aktualizace**
   - Integrace s SpecialistOrchestrator
   - Nové datové modely (BriefState)
   - Podpora canvas obsahu

### 🔄 Současný Stav

- ✅ Základní architektura implementována
- ✅ API funguje s novými funkcemi
- ✅ Testování dokončeno
- ⚠️ JSON parsing potřebuje doladění
- ⚠️ Supabase funkce pro multi-domain RAG

## 🚀 Nasazení do Produkce

### 1. Příprava Prostředí

```bash
# Aktualizace závislostí
cd backend
source ../venv/bin/activate
pip install -r requirements.txt

# Kontrola konfigurace
python -c "from specialist_orchestrator import get_specialist_orchestrator; print('✅ Orchestrator OK')"
python -c "from multi_domain_rag import get_multi_domain_rag; print('✅ Multi-Domain RAG OK')"
```

### 2. Konfigurace Supabase

```sql
-- Vytvoření funkcí pro multi-domain RAG (pokud neexistují)
CREATE OR REPLACE FUNCTION match_pruzkumy(
  query_embedding vector(1536),
  match_threshold float DEFAULT 0.78,
  match_count int DEFAULT 5
)
RETURNS TABLE (
  id bigint,
  content text,
  metadata jsonb,
  similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    kb_pruzkumy.id,
    kb_pruzkumy.content,
    kb_pruzkumy.metadata,
    1 - (kb_pruzkumy.embedding <=> query_embedding) AS similarity
  FROM kb_pruzkumy
  WHERE 1 - (kb_pruzkumy.embedding <=> query_embedding) > match_threshold
  ORDER BY kb_pruzkumy.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;
```

### 3. Aktualizace Proměnných Prostředí

```bash
# .env - přidat nové konfigurace
SPECIALIST_MODE=true
MULTI_DOMAIN_RAG=true
CANVAS_ENABLED=true

# Zachovat existující konfigurace
SUPABASE_URL=your_url
SUPABASE_KEY=your_key
OPENAI_API_KEY=your_key
```

### 4. Spuštění Serverů

```bash
# Backend
cd backend
python api_server.py

# Frontend (pokud používáte)
cd frontend
python -m http.server 8080
```

## 🔧 Řešení Problémů

### Problém: JSON Parsing Chyby

**Symptom:** `⚠️ Nepodařilo se parsovat JSON od strategického agenta`

**Řešení:**
1. Zkontrolujte prompt v `specialist_tasks.yaml`
2. Ujistěte se, že agent vrací validní JSON
3. Použijte fallback mechanismus

```python
# V specialist_orchestrator.py je implementován fallback
def _create_fallback_strategist_response(self, brief_state: BriefState) -> StrategistResponse:
    return StrategistResponse(
        thought_process="Fallback response",
        next_action="GATHER_INFO",
        # ...
    )
```

### Problém: Supabase Funkce Neexistují

**Symptom:** `Could not find the function public.match_pruzkumy`

**Řešení:**
1. Vytvořte chybějící funkce v Supabase
2. Nebo aktualizujte konfiguraci na existující funkce

```yaml
# V specialist_agents.yaml
knowledge_bases:
  - id: "default_gdanalyst"
    table_name: "gdanalyst"
    query_function: "match_gdanalyst"  # Použijte existující funkci
```

### Problém: Canvas Obsah se Nezobrazuje

**Symptom:** Frontend neobdrží canvas_content

**Řešení:**
1. Zkontrolujte API response
2. Ujistěte se, že frontend čte nové pole

```javascript
// Frontend kód
const response = await fetch('/chat', {
    method: 'POST',
    body: JSON.stringify({message: userInput})
});
const data = await response.json();

// Nové pole
const chatResponse = data.chat_response || data.question; // Fallback
const canvasContent = data.canvas_content;
```

## 📊 Monitoring a Metriky

### Klíčové Metriky

1. **Úspěšnost Orchestrace**
   ```python
   # Logování v specialist_orchestrator.py
   logger.info(f"✅ Orchestrace dokončena: {completion_percentage}% hotovo")
   ```

2. **Kvalita JSON Parsingu**
   ```python
   # Počet fallback responses
   logger.warning("⚠️ Nepodařilo se parsovat JSON od strategického agenta")
   ```

3. **RAG Dostupnost**
   ```python
   # Status multi-domain RAG
   rag_status = multi_domain_rag.get_status()
   ```

### Doporučené Alerty

- Vysoký počet JSON parsing chyb (>10%)
- RAG systém nedostupný
- Dlouhé response časy (>30s)
- Vysoká míra fallback responses

## 🔄 Rollback Plán

### V Případě Kritických Problémů

1. **Rychlý Rollback**
   ```bash
   # Přepnutí na původní systém
   export USE_CONVERSATIONAL_AGENT=true
   # Restart API serveru
   ```

2. **Postupný Rollback**
   - Deaktivace specialist mode
   - Návrat k původním endpoints
   - Zachování dat a sessions

### Zachování Dat

- Session data jsou kompatibilní
- Brief state lze převést na conversation state
- Historie chatu zůstává nedotčena

## 📈 Optimalizace Výkonu

### 1. Paralelizace Agentů

```python
# V budoucnu - současně interface a canvas
import asyncio

async def run_parallel_agents():
    interface_task = asyncio.create_task(self._run_interface_agent(...))
    canvas_task = asyncio.create_task(self._run_canvas_agent(...))
    
    interface_result, canvas_result = await asyncio.gather(
        interface_task, canvas_task
    )
```

### 2. Caching

```python
# Cache pro často používané responses
from functools import lru_cache

@lru_cache(maxsize=100)
def get_cached_canvas(brief_state_hash):
    # Cache canvas content pro stejné stavy
    pass
```

### 3. Model Optimalizace

- Použití rychlejších modelů pro interface agenta
- Caching embeddings pro RAG
- Batch processing pro více sessions

## 🎯 Další Kroky

### Krátkodobé (1-2 týdny)
1. Doladění JSON parsing
2. Optimalizace promptů
3. Monitoring implementace

### Střednědobé (1-2 měsíce)
1. Nové domény (sales, support)
2. Pokročilé analytics
3. A/B testování

### Dlouhodobé (3-6 měsíců)
1. Adaptivní agenti
2. Učení z interakcí
3. Personalizace

---

*Migration Guide vytvořen: 10.07.2025*
*Verze: 1.0*
